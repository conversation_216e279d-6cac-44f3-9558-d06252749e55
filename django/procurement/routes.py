# Starlette routes

from starlette.routing import Route
from .sse_views import (
    bidding_status_sse_view,
    rfq_status_update_sse_view,
    auction_updates_sse_view,
)

BASE_PATH = '/procurement/api/v1/procurement'

routes = [
    Route('/sse/bidding-status/', bidding_status_sse_view),
    Route('/sse/rfq/{rfq_id}/', rfq_status_update_sse_view),
    Route('/sse/rfq/{rfq_id}/auction-updates/', auction_updates_sse_view),
]
