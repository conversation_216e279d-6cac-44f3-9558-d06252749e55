import logging
from rest_framework import status
from pydantic import ValidationError
from django.conf import settings
from django.utils.decorators import method_decorator
from utils.mongo import MongoUtility
from authn.decorators import (
    validate_request_payload,
    validate_query_params,
    validate_data_access
)
from utils.constants import (
    AdminDBColls,
    ProcurementDBColls,
    RFQStatus,
    AttachmentType,
    SAASModules,
    FileTypes,
    LOIStatus
)
from utils import (
    format_error_response,
    format_response,
    validate_file_type_and_size,
    clean_filename,
    get_presigned_url,
    upload_to_s3,
    delete_from_s3,
    get_uuid,
    DateUtil
)
from authn import (
    AuthenticateAll,
    AuthenticateSeeker,
    Permissions,
    PermissionedAPIView
)
from schema import (
    DraftRFQSchema,
    RFQSchema,
    RFQAttachmentSchema,
    RFQProviderSchema,
    LaneProviderSchema
)
from .request_validators import UpdateRFQPayloadValidator, ListRFQsParamsValidator
from .request_filters import RFQFilters
from notifications.manager import send_create_rfq_email

logger = logging.getLogger('application')


class DashboardCardsAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.Procurement.VIEW, ['GET'])
    ]

    def get(self, request, *args, **kwargs):
        inst = RFQFilters(request)

        query = inst._filter_by_company_type({})

        db = MongoUtility()

        active_auctions = {'name': 'Active Auctions', 'value': 0}
        completed_auctions = {'name': 'Completed Auctions', 'value': 0}

        for item in inst.get_total_counts(query):
            if item['rfq_status'] in [RFQStatus.ACTIVE.value, RFQStatus.BIDDING_IN_PROGRESS.value]:
                active_auctions['value'] += item['count']
            elif item['rfq_status'] == RFQStatus.COMPLETED.value:
                completed_auctions['value'] = item['count']

        if request.is_seeker:
            doc = db.find(ProcurementDBColls.AUCTION_SAVINGS, {'company_id': request.company_id}, find_one=True)

            avg_savings = {
                'name': 'Average Savings',
                'value': f"{doc.get('avg_savings_percent') or 0} %"
            }
            response_data = [active_auctions, completed_auctions, avg_savings]
        else:
            lp_query = {
                'provider_id': request.company_id,
                'loi_status_id': LOIStatus.ACCEPTED.value
            }
            won_auctions = {
                'name': 'My Performance',
                'value': db.find(ProcurementDBColls.LANE_PROVIDERS, lp_query).count(),
                'sub_headers': [
                    {'name': 'Lanes received', 'value': 0},
                    {'name': 'Winning %', 'value': '0 %'},
                    {'name': "Value of LOI's", 'value': 0},
                ]
            }
            response_data = [active_auctions, won_auctions, completed_auctions]

        return format_response(status.HTTP_200_OK, response_data, 'Success')


@method_decorator(validate_query_params(ListRFQsParamsValidator), name='get')
class ListRFQsAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.Procurement.VIEW, ['GET'])
    ]

    def get(self, request, *args, **kwargs):
        params = request.validated_params

        inst = RFQFilters(request)

        rfq_docs = inst.run()
        total_count = rfq_docs.count()

        response_data = {
            'rfqs': [rfq for rfq in rfq_docs],
            'total_count': total_count,
            'limit': params.limit,
            'offset': params.offset,
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


@method_decorator(validate_query_params(ListRFQsParamsValidator), name='get')
class CountRFQsAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.Procurement.VIEW, ['GET'])
    ]

    def get(self, request, *args, **kwargs):
        inst = RFQFilters(request)

        try:
            query = inst.run(get_query=True)
        except ValueError as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                str(e)
            )

        response_data = {
            'totals': inst.get_total_counts(query)
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class CreateDraftRFQAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.CREATE_RFQ, ['POST']),
    ]

    def post(self, request, *args, **kwargs):
        db = MongoUtility()

        rfq_data = {
            'company_id': request.company_id,
            'company_name': request.company_name,
            'created_by': request.user_name,
            'created_by_id': request.user_id,
            'rfq_status': RFQStatus.DRAFT.value
        }

        try:
            rfq_doc = DraftRFQSchema(**rfq_data).model_dump()
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

        db.insert(ProcurementDBColls.RFQ, [rfq_doc])
        rfq_doc.pop('_id', None)

        response_data = rfq_doc
        return format_response(status.HTTP_200_OK, response_data, 'Created Draft RFQ')


class GetRFQDetailsAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.Procurement.VIEW, ['GET']),
    ]

    @validate_data_access(['rfq'])
    def get(self, request, *args, **kwargs):
        if request.is_provider:
            db = MongoUtility()
            query = {
                'rfq_id': kwargs['rfq_id'],
                'provider_id': request.company_id,
                'rfq_seen_on': None
            }
            db.update(ProcurementDBColls.RFQ_PROVIDERS, query, {'rfq_seen_on': request.now, 'updated_on': request.now})

            del query['rfq_seen_on']
            lane_provider_doc = db.find(ProcurementDBColls.LANE_PROVIDERS, query, find_one=True)
            request.rfq_doc['is_tnc_accepted'] = lane_provider_doc['is_tnc_accepted']

        response_data = request.rfq_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class ListRFQProvidersAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.VIEW, ['GET']),
    ]

    @validate_data_access(['rfq'])
    def get(self, request, *args, **kwargs):
        db = MongoUtility()

        query = {'rfq_id': kwargs['rfq_id'], 'company_id': request.company_id}

        rp_docs = list(db.find(ProcurementDBColls.RFQ_PROVIDERS, query))

        response_data = {
            'rfqs': rp_docs,
            'total_count': len(rp_docs)
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


@method_decorator(validate_request_payload(UpdateRFQPayloadValidator), name='put')
class UpdateDeleteRFQAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.UPDATE_RFQ, ['PUT']),
        (Permissions.Procurement.DELETE_RFQ, ['DELETE']),
    ]

    @validate_data_access(['rfq'])
    def put(self, request, *args, **kwargs):
        rfq_id = kwargs['rfq_id']
        payload = request.validated_payload.model_dump()
        rfq_doc = request.rfq_doc

        self.db = MongoUtility()
        self.admin_db = MongoUtility(module_id=SAASModules.ADMIN.value)

        rfq_query = {'id': rfq_id, 'company_id': request.company_id}

        if rfq_doc['rfq_status'] != RFQStatus.DRAFT.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Only Draft RFQ can be updated.')

        payload['provider_ids'] = list(set(payload['provider_ids']))
        payload.pop('attachments', None)

        rfq_doc.update(payload)
        rfq_doc.pop('updated_on', None)

        if not payload['is_confirmed']:
            updated_rfq = DraftRFQSchema(**rfq_doc).model_dump()
        else:
            provider_ids = rfq_doc['provider_ids']
            if not provider_ids:
                return format_error_response(status.HTTP_400_BAD_REQUEST, 'provider_ids cannot be empty.')

            query = {'seeker_id': request.company_id, 'provider_id': {'$in': provider_ids}}
            df = {'_id': 0, 'provider_id': 1, 'provider_name': 1}
            providers_id_name_map = {x['provider_id']: x['provider_name'] for x in self.admin_db.find(AdminDBColls.COMPANY_MAPPING, query, df)}

            try:
                updated_rfq = RFQSchema(**rfq_doc).model_dump()
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

            try:
                lane_id_no_map = self.assign_lane_numbers(request.company_id, rfq_id)
                rfq_providers = self.get_rfq_providers(request.company_id, rfq_id, provider_ids, providers_id_name_map)
                lane_providers = self.get_lane_providers(request, rfq_id, provider_ids, providers_id_name_map, lane_id_no_map)
            except ValueError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

            # set the rfq to "Active" status on confirm
            updated_rfq['rfq_status'] = RFQStatus.ACTIVE.value
            updated_rfq.pop('attachments', None)

            self.db.insert(ProcurementDBColls.RFQ_PROVIDERS, rfq_providers, insert_many=True)
            self.db.insert(ProcurementDBColls.LANE_PROVIDERS, lane_providers, insert_many=True)

        updated_rfq_doc = self.db.update(ProcurementDBColls.RFQ, rfq_query, updated_rfq, find_one_and_update=True)

        # Send Create RFQ notification when RFQ is confirmed
        if payload['is_confirmed']:
            try:
                notification_result = send_create_rfq_email(
                    rfq_id=updated_rfq_doc['id'],
                    rfq_no=updated_rfq_doc['rfq_no'],
                    company_id=updated_rfq_doc['company_id'],
                    company_name=updated_rfq_doc['company_name'],
                    rfq_title=updated_rfq_doc.get('rfq_title', ''),
                    bidding_start_time=updated_rfq_doc.get('bidding_start_time'),
                    bidding_end_time=updated_rfq_doc.get('bidding_end_time'),
                    total_lanes=len(lane_id_no_map),
                    provider_ids=updated_rfq_doc['provider_ids'],
                    send_email=True,
                    send_sms=False
                )

                if notification_result['email_sent']:
                    logger.info(f"Create RFQ notification sent successfully for RFQ {updated_rfq_doc['id']}")
                else:
                    logger.warning(f"Failed to send Create RFQ notification for RFQ {updated_rfq_doc['id']}")
            except Exception as e:
                logger.error(f"Error sending Create RFQ notification for RFQ {updated_rfq_doc['id']}: {str(e)}")

        response_data = updated_rfq_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    def assign_lane_numbers(self, company_id, rfq_id):
        query = {'rfq_id': rfq_id, 'company_id': company_id}
        lane_id_no_map = {}
        for lane_no, lane in enumerate(self.db.find(ProcurementDBColls.LANES, query, sort=[('_id', 1)]), 1):
            lane_query = {
                'id': lane['id'],
                'rfq_id': rfq_id,
                'company_id': company_id,
            }
            lane_id_no_map[lane['id']] = lane_no
            self.db.add_write_operation(lane_query, {'lane_no': lane_no}, update_one=True)

        if not lane_id_no_map:
            raise ValueError('Please add trips before submitting.')

        self.db.bulk_write(ProcurementDBColls.LANES)
        return lane_id_no_map

    def get_rfq_providers(self, company_id, rfq_id, provider_ids, providers_map):
        rfq_providers = []
        for provider_id in provider_ids:
            try:
                provider_name = providers_map[provider_id]
            except KeyError:
                raise ValueError(f'Provider({provider_id}) not mapped with Seeker Company.')

            obj = {
                'rfq_id': rfq_id,
                'company_id': company_id,
                'provider_id': provider_id,
                'provider_name': provider_name,
            }
            rfq_provider = RFQProviderSchema(**obj).model_dump()
            rfq_providers.append(rfq_provider)
        return rfq_providers

    def get_lane_providers(self, request, rfq_id, provider_ids, providers_map, lane_id_no_map):
        query = {'rfq_id': rfq_id, 'company_id': request.company_id}
        lane_ids = self.db.distinct(ProcurementDBColls.LANES, 'id', query)

        lane_providers = []
        for lane_id in lane_ids:
            for provider_id in provider_ids:
                try:
                    provider_name = providers_map[provider_id]
                except KeyError:
                    raise ValueError(f'Provider({provider_id}) not mapped with Seeker Company.')

                obj = {
                    'rfq_id': rfq_id,
                    'lane_id': lane_id,
                    'lane_no': lane_id_no_map[lane_id],
                    'company_id': request.company_id,
                    'company_name': request.company_name,
                    'provider_id': provider_id,
                    'provider_name': provider_name,
                }
                lane_provider = LaneProviderSchema(**obj).model_dump()
                lane_providers.append(lane_provider)
        return lane_providers

    @validate_data_access(['rfq'])
    def delete(self, request, *args, **kwargs):
        rfq_id = kwargs['rfq_id']
        rfq_doc = request.rfq_doc

        db = MongoUtility()

        query = {'id': rfq_id, 'company_id': request.company_id}

        if rfq_doc['rfq_status'] != RFQStatus.DRAFT.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Only Draft RFQ can be deleted.')

        for att in rfq_doc['attachments']:
            delete_from_s3(file_url=att['url'])

        lane_query = {'rfq_id': rfq_id, 'company_id': request.company_id}
        db.delete(ProcurementDBColls.LANE_PROVIDERS, lane_query, delete_many=True)
        db.delete(ProcurementDBColls.LANES, lane_query, delete_many=True)

        db.delete(ProcurementDBColls.RFQ, query)
        return format_response(status.HTTP_200_OK, {}, 'Success')


class GetRFQAttachmentAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.Procurement.VIEW, ['GET']),
    ]

    @validate_data_access(['rfq'])
    def get(self, request, *args, **kwargs):
        attachment_id = kwargs['attachment_id']
        direct_download = request.GET.get('action', 'preview') == 'download'
        rfq_doc = request.rfq_doc

        for attachment in rfq_doc['attachments']:
            if attachment['id'] != attachment_id:
                continue

            url, error = get_presigned_url(
                file_url=attachment['url'],
                for_download=direct_download,
                s3_bucket=settings.SAAS_S3_BUCKET,
                link_expiry=settings.PRESIGNED_LINK_EXPIRY
            )
            if error:
                return format_error_response(status.HTTP_400_BAD_REQUEST, error)

            response_data = {'url': url}
            return format_response(status.HTTP_200_OK, response_data, 'Attachment link generated successfully')

        return format_error_response(status.HTTP_404_NOT_FOUND, "Attachment not found.")


class CopyRFQAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.CREATE_RFQ, ['POST']),
    ]

    @validate_data_access(['rfq'])
    def post(self, request, *args, **kwargs):
        rfq_id = kwargs['rfq_id']
        source_rfq = request.rfq_doc
        now = DateUtil.get_current_timestamp()

        # Verify that the user has permission to copy this RFQ
        if source_rfq['company_id'] != request.company_id:
            return format_error_response(status.HTTP_403_FORBIDDEN, 'You do not have permission to copy this RFQ.')

        # Create a copy of the RFQ with reset fields
        new_rfq_data = source_rfq.copy()
        new_rfq_data.update({
            'id': get_uuid(),
            'bidding_start_time': None,
            'bidding_end_time': None,
            'contract_tenure': '',
            'contract_tenure_id': None,
            'contract_start_date': None,
            'contract_end_date': None,
            'rfq_status': RFQStatus.DRAFT.value,
            'attachments': [],
            'provider_ids': [],
            'chp_sent': 0,
            'chp_accepted': 0,
            'chp_rejected': 0,
            'cop_sent': 0,
            'cop_accepted': 0,
            'cop_rejected': 0,
            'loi_sent': 0,
            'loi_accepted': 0,
            'loi_rejected': 0,
            'loi_ignored': 0,
            'created_by': request.user_name,
            'created_by_id': request.user_id,
            'created_on': now,
            'updated_on': now,
            'rfq_no': None  # New RFQ No. will be auto-generated by the schema
        })

        db = MongoUtility()

        try:
            # Use DraftRFQSchema to validate and create the new RFQ
            new_rfq = DraftRFQSchema(**new_rfq_data).model_dump()
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

        # Insert the new RFQ into the database
        db.insert(ProcurementDBColls.RFQ, [new_rfq])
        new_rfq.pop('_id', None)

        # Copy lanes if they exist
        lane_query = {'rfq_id': rfq_id, 'company_id': request.company_id}
        lanes = db.find(ProcurementDBColls.LANES, lane_query)

        new_lanes = []
        for lane in lanes:
            new_lane = lane.copy()
            new_lane.update({
                'id': get_uuid(),
                'rfq_id': new_rfq['id'],
                'placement_time': None,
                'participation': 0,
                'bid_comp_percent': 0,
                'bid_comp': None,
                'lpp': None,
                'l1_hits': 0,
                'l1_rate': None,
                'plain_hits': 0,
                'created_on': now,
                'updated_on': now,
            })
            new_lanes.append(new_lane)

        if new_lanes:
            db.insert(ProcurementDBColls.LANES, new_lanes, insert_many=True)

        response_data = new_rfq
        return format_response(status.HTTP_200_OK, response_data, 'RFQ copied successfully')


class UploadDeleteRFQAttachmentAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.UPDATE_RFQ, ['POST']),
        (Permissions.Procurement.UPDATE_RFQ, ['DELETE']),
    ]

    @validate_data_access(['rfq'])
    def post(self, request, *args, **kwargs):
        rfq_id = kwargs['rfq_id']
        attachment_type = request.data.get('attachment_type') or AttachmentType.CONTRACT

        db = MongoUtility()

        query = {'id': rfq_id, 'company_id': request.company_id}

        files = request.data.getlist('files', [])
        if not files:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Please attach file(s) to upload.')

        allowed_types = [FileTypes.JPG.value, FileTypes.JPEG.value, FileTypes.PNG.value, FileTypes.PDF.value, FileTypes.DOC.value, FileTypes.DOCX.value]
        new_attachments = []
        for file in files:
            is_file_valid, errors = validate_file_type_and_size(file, allowed_types)
            if not is_file_valid:
                return format_error_response(status.HTTP_400_BAD_REQUEST, errors[0])

            original_file_name = file.name
            stored_file_name = clean_filename(original_file_name, add_timestamp=True)
            file_path = '{}/{}/{}/{}/{}'.format(
                settings.ENVIRONMENT_ENV,
                SAASModules.PROCUREMENT.name.lower(),
                request.company_id,
                rfq_id,
                stored_file_name
            )

            s3_path, s3_error = upload_to_s3(file, file_path)
            if s3_error:
                return format_error_response(status.HTTP_400_BAD_REQUEST, s3_error)

            new_attachment = RFQAttachmentSchema(**{
                'file_name': original_file_name,
                'file_size': file.size,
                'type': attachment_type,
                'url': s3_path,
                'created_by_id': request.user_id,
                'created_by': request.user_name
            }).model_dump()
            new_attachments.append(new_attachment)

        push_query = {'attachments': {'$each': new_attachments}}
        set_query = {'updated_on': request.now}
        updated_rfq = db.update(ProcurementDBColls.RFQ, query, set_query=set_query, push_query=push_query, find_one_and_update=True)

        response_data = {
            'attachments': updated_rfq['attachments']
        }
        return format_response(status.HTTP_200_OK, response_data, 'Attachment(s) uploaded successfully.')

    @validate_data_access(['rfq'])
    def delete(self, request, *args, **kwargs):
        rfq_id = kwargs['rfq_id']
        attachment_id = kwargs['attachment_id']
        rfq_doc = request.rfq_doc

        db = MongoUtility()

        query = {'id': rfq_id, 'company_id': request.company_id}

        try:
            attachment = [x for x in rfq_doc['attachments'] if x['id'] == attachment_id][0]
        except (KeyError, IndexError):
            attachment = {}

        if attachment:
            error = delete_from_s3(file_url=attachment['url'])

            delete_entry = (not error) or ('does not exist' in error.lower())
            if not delete_entry:
                return format_error_response(status.HTTP_400_BAD_REQUEST, error)

            pull_query = {'attachments': {'id': attachment_id}}
            set_query = {'updated_on': request.now}
            db.update(ProcurementDBColls.RFQ, query, set_query=set_query, pull_query=pull_query)

        return format_response(status.HTTP_200_OK, {}, 'Attachment deleted successfully.')
