import logging
import xlsxwriter
import time
import math
from datetime import datetime
from rest_framework import status
from pydantic import ValidationError
from django.utils.decorators import method_decorator
from django.http import HttpResponse
from io import BytesIO
from utils.mongo import MongoUtility
from authn.decorators import (
    validate_request_payload,
    validate_query_params,
    validate_data_access
)
from utils.constants import (
    AdminDBColls,
    ProcurementDBColls,
    RFQStatus,
    StopPointType,
    LongMIMETypes,
    FileTypes, TatUOM,
    BidStatus, BiddingCategory,
    VendorBiddingScreen,
)
from utils import (
    format_error_response,
    format_response,
    get_uuid,
    validate_file_type_and_size,
    get_search_results_from_google_geocode_api,
    get_search_results_from_google_places_api,
    get_distance_with_waypoints,
    Memoization
)
from authn import (
    AuthenticateAll,
    AuthenticateSeeker,
    Permissions,
    PermissionedAPIView
)
from schema import LaneSchema
from .request_validators import (
    CreateUpdateLanePayloadValidator,
    CreateUpdateStopPointPayloadValidator,
    ListLanesParamsValidator
)
from .app_utils import get_product_settings

logger = logging.getLogger('application')

MAX_LANES_PER_TEMPLATE = 25


@method_decorator(validate_query_params(ListLanesParamsValidator), name='get')
class ListLanesAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.Procurement.VIEW, ['GET'])
    ]

    @validate_data_access(['rfq'])
    def get(self, request, *args, **kwargs):
        rfq_id = kwargs['rfq_id']
        rfq_doc = request.rfq_doc
        params = request.validated_params
        product_settings = get_product_settings(rfq_doc['company_id'])
        show_tat = product_settings.get('enable_tat_visibility')
        is_unmonitored_bidding = product_settings.get('bidding_category_id') == BiddingCategory.UNMONITORED.value
        vendor_bidding_screen_id = product_settings.get('vendor_bidding_screen_id')
        show_rank_indicator = vendor_bidding_screen_id == VendorBiddingScreen.RANK_INDICATOR.value
        show_rank = vendor_bidding_screen_id in [
            VendorBiddingScreen.RANK_INDICATOR.value,
            VendorBiddingScreen.LIVE_STATUS.value,
            VendorBiddingScreen.L1_AND_LIVE_STATUS.value,
        ]
        show_l1_rate = vendor_bidding_screen_id in [
            VendorBiddingScreen.L1_RATE.value,
            VendorBiddingScreen.L1_AND_LIVE_STATUS.value,
        ]

        db = MongoUtility()

        lp_query = {}
        lane_df, lp_df = {'_id': 0}, {'_id': 0}

        if request.is_provider:
            lp_query['provider_id'] = request.company_id

            if not show_tat:
                lane_df.update({'tat': 0, 'tat_uom': 0, 'tat_uom_id': 0})

            if not show_l1_rate:
                lane_df['l1_rate'] = 0

        lane_query = {'rfq_id': rfq_id}
        if params.search_term:
            lane_query['$or'] = [
                {'src_city': {'$regex': params.search_term, '$options': 'i'}},
                {'dst_city': {'$regex': params.search_term, '$options': 'i'}},
            ]

        lane_docs = db.find(ProcurementDBColls.LANES, lane_query, lane_df).limit(params.limit).skip(params.offset)
        total_lanes = lane_docs.count()

        lane_id_index_map, lanes = {}, []
        for index, lane in enumerate(lane_docs):
            lane['providers'] = []
            lanes.append(lane)
            lane_id_index_map[lane['id']] = index

        lp_query['lane_id'] = {'$in': list(lane_id_index_map.keys())}
        lane_provider_docs = list(db.find(ProcurementDBColls.LANE_PROVIDERS, lp_query, lp_df))

        # Sort on bid_price by lowest first order.
        # (x['bid_price'] is None): Moves None values to the end.
        # x['bid_price']: Sorts in ascending order.
        # x['updated_on']: Ensures oldest first order when bid prices are the same.
        primary_sort_key = 'bid_price' if rfq_doc['rfq_status'] != RFQStatus.COMPLETED.value else 'loi_price'
        lane_provider_docs.sort(key=lambda x: (x[primary_sort_key] is None, x[primary_sort_key], x['updated_on']))

        if request.is_provider:
            for lane_provider in lane_provider_docs:
                lane_provider['is_ranked'] = bool(lane_provider['priority'])

                if not show_rank:
                    lane_provider['priority'] = None

                if show_rank_indicator:
                    rank_indicator = 'green' if lane_provider['priority'] == 1 else 'orange'
                    lane_provider.update({
                        'priority': None,
                        'rank_indicator': rank_indicator
                    })

                lane_index = lane_id_index_map[lane_provider['lane_id']]
                lanes[lane_index]['providers'].append(lane_provider)
        else:
            for lane_provider in lane_provider_docs:
                lane_index = lane_id_index_map[lane_provider['lane_id']]

                # in complete bidding solution, provider is not ranked
                # unless he places bids in all lanes
                if (not lane_provider['priority']) or is_unmonitored_bidding:
                    lane_provider.update({
                        'ip': None,
                        'provider_id': None,
                        'provider_name': None,
                        'is_tnc_accepted': False,
                        'priority': None,
                        'bid_price': None,
                        'loi_price': None,
                        'savings_amt': None,
                        'savings_percent': None,
                        'was_l1': False,
                        'bid_status_id': BidStatus.NO_BID.value,
                        'bid_status': BidStatus.NO_BID.name,
                        'bid_withdraw_reason': None,
                        'updated_on': None,
                        'created_on': None
                    })

                lanes[lane_index]['providers'].append(lane_provider)

        response_data = {
            'lanes': lanes,
            'total_count': total_lanes,
            'limit': params.limit,
            'offset': params.offset,
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


@method_decorator(validate_request_payload(CreateUpdateLanePayloadValidator), name='post')
@method_decorator(validate_request_payload(CreateUpdateLanePayloadValidator), name='put')
class LaneCRUDAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.VIEW, ['GET']),
        (Permissions.Procurement.CREATE_LANE, ['POST']),
        (Permissions.Procurement.UPDATE_LANE, ['PUT']),
        (Permissions.Procurement.DELETE_LANE, ['DELETE']),
    ]

    @validate_data_access(['rfq', 'lane'])
    def get(self, request, *args, **kwargs):
        response_data = request.lane_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    @validate_data_access(['rfq'])
    def post(self, request, *args, **kwargs):
        rfq_id = kwargs['rfq_id']
        payload = request.validated_payload.model_dump()

        db = MongoUtility()

        payload['rfq_id'] = rfq_id
        payload['company_id'] = request.company_id
        payload['created_on'] = payload['updated_on']

        try:
            self.update_location_details(lane_payload=payload)
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

        try:
            lane_doc = LaneSchema(**payload).model_dump()
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

        db.insert(ProcurementDBColls.LANES, [lane_doc])
        lane_doc.pop('_id', None)

        return format_response(status.HTTP_200_OK, lane_doc, 'Success')

    @validate_data_access(['rfq', 'lane'])
    def put(self, request, *args, **kwargs):
        rfq_id = kwargs['rfq_id']
        lane_id = kwargs['lane_id']
        payload = request.validated_payload.model_dump()
        lane_doc = request.lane_doc

        db = MongoUtility()

        lane_query = {'id': lane_id, 'rfq_id': rfq_id, 'company_id': request.company_id}

        payload['id'] = lane_id
        payload['rfq_id'] = rfq_id

        try:
            self.update_location_details(lane_payload=payload, old_lane_doc=lane_doc)
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

        try:
            lane_doc.update(payload)
            updated_lane = LaneSchema(**lane_doc).model_dump()
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

        updated_lane_doc = db.update(ProcurementDBColls.LANES, lane_query, updated_lane, find_one_and_update=True)

        response_data = updated_lane_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    def fetch_and_update_payload_with_geocode_data(self, place_id, lat=None, lng=None, prefix=''):
        results = get_search_results_from_google_geocode_api(place_id=place_id)
        try:
            address_details = results['results'][0]
        except IndexError:
            logger.error(f"Error fetching location details | place_id: {place_id} | status: {results['status']} | msg: {results['error_message']}")
            raise ValueError(f"Unable to fetch location details: {results['error_message']}")

        if (not address_details['pincode']) and (lat and lng):
            new_results = get_search_results_from_google_geocode_api(lat=lat, lng=lng)
            address_details['pincode'] = new_results['results'][0]['pincode']

        return {
            f'{prefix}pincode': address_details['pincode'],
            f'{prefix}city': address_details['city'],
            f'{prefix}state': address_details['state'],
            f'{prefix}state_code': address_details['state_code'],
        }

    def update_location_details(self, lane_payload, old_lane_doc=None):
        if not old_lane_doc:
            old_lane_doc = {}

        old_src_place_id = old_lane_doc.get('src_place_id')
        old_dst_place_id = old_lane_doc.get('dst_place_id')
        old_stop_points_map = {x['id']: x for x in old_lane_doc.get('stop_points', [])}

        new_src_place_id = lane_payload.get('src_place_id')
        new_src_lat, new_src_lng = lane_payload.get('src_lat'), lane_payload.get('src_lng')

        new_dst_place_id = lane_payload.get('dst_place_id')
        new_dst_lat, new_dst_lng = lane_payload.get('dst_lat'), lane_payload.get('dst_lng')

        if (old_src_place_id != new_src_place_id) or (not lane_payload.get('src_pincode')):
            data = self.fetch_and_update_payload_with_geocode_data(new_src_place_id, new_src_lat, new_src_lng, prefix='src_')
            lane_payload.update(data)

        if (old_dst_place_id != new_dst_place_id) or (not lane_payload.get('dst_pincode')):
            data = self.fetch_and_update_payload_with_geocode_data(new_dst_place_id, new_dst_lat, new_dst_lng, prefix='dst_')
            lane_payload.update(data)

        for new_stop_point in lane_payload.get('stop_points', []):
            stop_point_id = new_stop_point.get('id')
            new_place_id = new_stop_point['place_id']
            new_lat, new_lng = new_stop_point.get('lat'), new_stop_point.get('lng')

            old_stop_point = old_stop_points_map.get(stop_point_id, {})
            old_place_id = old_stop_point.get('place_id')

            if old_place_id and (new_place_id == old_place_id) and old_stop_point.get('pincode'):
                # make sure old data is not overwritten in case there aren't any new changes
                new_stop_point.update(old_stop_point)
                continue

            data = self.fetch_and_update_payload_with_geocode_data(new_place_id, new_lat, new_lng)
            new_stop_point.update(data)

    @validate_data_access(['rfq', 'lane'])
    def delete(self, request, *args, **kwargs):
        rfq_id = kwargs['rfq_id']
        lane_id = kwargs['lane_id']
        rfq_doc = request.rfq_doc

        db = MongoUtility()

        if rfq_doc['rfq_status'] != RFQStatus.DRAFT.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Only Draft RFQ can be changed.')

        lane_query = {'id': lane_id, 'rfq_id': rfq_id, 'company_id': request.company_id}

        db.delete(ProcurementDBColls.LANES, lane_query)

        lane_query['lane_id'] = lane_query.pop('id')
        db.delete(ProcurementDBColls.LANE_PROVIDERS, lane_query, delete_many=True)

        return format_response(status.HTTP_200_OK, {}, 'Success')


class UploadLanesAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.CREATE_LANE, ['POST']),
    ]

    def _get_column_letter(self, col_idx):
        """Convert a zero-based column index to an Excel column letter (A, B, C, ... Z, AA, AB, etc.)"""
        result = ""
        while col_idx >= 0:
            remainder = col_idx % 26
            result = chr(65 + remainder) + result  # 65 is ASCII for 'A'
            col_idx = col_idx // 26 - 1
        return result

    def _get_place_id_from_pincode(self, pincode):
        """Get place ID from pincode using Google Places API"""
        if not pincode:
            return None

        # Convert pincode to string if it's a number
        pincode_str = str(pincode)

        # Search for the pincode in Google Places API
        search_term = f"{pincode_str} India"
        results = get_search_results_from_google_places_api(search_term)

        if not results['is_success'] or results['count'] == 0:
            logger.warning(f"No place found for pincode: {pincode_str}")
            return None

        # Return the place_id of the first result
        return results['results'][0]['place_id']

    def _calculate_distance_and_tat(self, lane):
        """Calculate distance and TAT between source, destination, and stop points"""
        try:
            # Get place IDs for source and destination
            src_place_id = self._get_place_id_from_pincode(lane.get('src_pincode'))
            dst_place_id = self._get_place_id_from_pincode(lane.get('dst_pincode'))

            if not src_place_id or not dst_place_id:
                logger.warning(f"Missing place ID for source or destination")
                return

            # Format place IDs for the API
            origin = f"place_id:{src_place_id}"
            destination = f"place_id:{dst_place_id}"

            # Get place IDs for stop points and format waypoints
            waypoints = []
            for stop_point in lane.get('stop_points', []):
                stop_point_place_id = self._get_place_id_from_pincode(stop_point.get('pincode'))
                if stop_point_place_id:
                    waypoints.append(f"place_id:{stop_point_place_id}")

            # Format waypoints for the API
            waypoints_str = "|".join(waypoints) if waypoints else None

            # Calculate distance and duration
            result = get_distance_with_waypoints(origin, destination, waypoints_str)

            if not result['is_success']:
                logger.warning(f"Failed to calculate distance: {result['error_message']}")
                return

            # Update lane with distance and TAT
            distance_km = result['total_distance'] / 1000  # Convert meters to kilometers

            # Calculate TAT based on average speed and other factors
            avg_speed = 20  # km/h (default value)
            hours_per_day = 15  # hours (default value)
            loading_time = 0  # hours (default value)
            unloading_time = 0  # hours (default value)
            stop_time = 0  # hours per waypoint (default value)

            # Calculate driving time based on average speed
            driving_hours = distance_km / avg_speed

            # Add loading, unloading, and stop times
            total_hours = driving_hours + loading_time + unloading_time + (len(waypoints) * stop_time)

            # Convert to days (rounded up to the nearest day)
            tat_days = math.ceil(total_hours / hours_per_day)

            # Update lane with calculated values
            lane['distance'] = int(round(distance_km, 0))
            lane['tat'] = tat_days

            # Add place IDs to lane and stop points
            lane['src_place_id'] = src_place_id
            lane['dst_place_id'] = dst_place_id

            # Update stop points with place IDs
            for i, stop_point in enumerate(lane.get('stop_points', [])):
                if i < len(waypoints) and waypoints[i].startswith("place_id:"):
                    stop_point['place_id'] = waypoints[i].replace("place_id:", "")

            return True
        except Exception as e:
            logger.error(f"Error calculating distance and TAT: {str(e)}")
            return False

    @validate_data_access(['rfq'])
    def post(self, request, *args, **kwargs):
        rfq_doc = request.rfq_doc
        rfq_id = rfq_doc['id']

        # Check if file is in the request
        if 'file' not in request.FILES:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'No file uploaded')

        uploaded_file = request.FILES['file']

        # Validate file type
        is_file_valid, errors = validate_file_type_and_size(uploaded_file, allowed_types=[FileTypes.XLSX.value])
        if not is_file_valid:
            return format_error_response(status.HTTP_400_BAD_REQUEST, errors[0])

        # Get RFQ document to validate it exists and belongs to the company
        db = MongoUtility()

        # Check if RFQ is in draft status
        if rfq_doc['rfq_status'] != RFQStatus.DRAFT.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Only Draft RFQ can be modified')

        try:
            # Read the Excel file using pandas
            import pandas as pd

            # Read the 'Lanes' sheet
            df = pd.read_excel(uploaded_file, sheet_name='Lanes', engine='openpyxl')

            # Remove rows with all NaN values
            df = df.dropna(how='all')

            # Reset index
            df = df.reset_index(drop=True)

            # Initialize errors list
            errors_list = []

            # Create a mapping of column names to Excel column letters
            column_mapping = {}
            for col_idx, col_name in enumerate(df.columns):
                column_letter = self._get_column_letter(col_idx)
                column_mapping[col_name] = column_letter

            # Initialize lanes list
            lanes = []

            # Process each row
            for index, row in df.iterrows():
                # Skip empty rows
                if pd.isna(row).all():
                    continue

                # Create a lane object
                lane = {
                    'id': get_uuid(),
                    'rfq_id': rfq_id,
                    'company_id': request.company_id,
                    'created_on': request.now,
                    'updated_on': request.now,
                    'stop_points': [],
                    'tat_uom': TatUOM.DAYS.value,
                    'tat_uom_id': TatUOM.DAYS.value
                }

                # Process basic lane fields
                try:
                    # Handle date and time fields
                    placement_date = row.get('Placement Date')
                    placement_time = row.get('Placement Time')

                    # Convert placement date and time to timestamp for schema validation
                    if not pd.isna(placement_date):
                        try:
                            # Parse date
                            if isinstance(placement_date, datetime):
                                date_obj = placement_date
                            else:
                                # Try to parse string date
                                date_str = str(placement_date)
                                date_obj = datetime.strptime(date_str, '%d/%m/%Y')

                            # Parse time if available
                            time_obj = None
                            if not pd.isna(placement_time):
                                if isinstance(placement_time, datetime):
                                    time_obj = placement_time
                                else:
                                    time_str = str(placement_time)
                                    time_obj = datetime.strptime(time_str, '%H:%M:%S')

                            # Combine date and time
                            if time_obj:
                                combined_datetime = datetime.combine(
                                    date_obj.date(),
                                    time_obj.time()
                                )
                            else:
                                combined_datetime = date_obj

                            # Convert to timestamp (seconds since epoch)
                            lane['placement_time'] = int(time.mktime(combined_datetime.timetuple())) * 1000
                        except Exception as e:
                            errors_list.append(f'Row {index + 2}: Error processing date/time - {str(e)}')

                    # Required fields validation
                    required_fields = [
                        ('Number of Vehicles', 'no_of_vehicles', int),
                        ('Vehicle Type', 'vehicle_type', str),
                        ('Vehicle Type ID', 'vehicle_type_id', str),
                        ('Vehicle Capacity (MT)', 'vehicle_capacity', float),
                        ('Vehicle Body Type', 'body_type', str),
                        ('Vehicle Body Type ID', 'body_type_id', str),
                        ('Source Address', 'src_address', str),
                        ('Source City', 'src_city', str),
                        ('Source State', 'src_state', str),
                        ('Source State Code', 'src_state_code', str),
                        ('Source Pincode', 'src_pincode', str),
                        ('Destination Address', 'dst_address', str),
                        ('Destination City', 'dst_city', str),
                        ('Destination State', 'dst_state', str),
                        ('Destination State Code', 'dst_state_code', str),
                        ('Destination Pincode', 'dst_pincode', str),
                        ('Ceiling Price', 'rate', float),
                        ('Business Volume', 'volume', float),
                        ('Business Volume UOM', 'volume_uom', str),
                        ('Business Volume UOM ID', 'volume_uom_id', str)
                    ]

                    for excel_field, db_field, field_type in required_fields:
                        value = row.get(excel_field)
                        column_letter = column_mapping.get(excel_field, '?')
                        if pd.isna(value):
                            errors_list.append(f'Row {index + 2}, Column {column_letter} ({excel_field}): Value is required')
                        else:
                            try:
                                # Convert to the appropriate type
                                if field_type == int:
                                    lane[db_field] = int(value)
                                elif field_type == float:
                                    lane[db_field] = float(value)
                                else:
                                    lane[db_field] = str(value)
                            except (ValueError, TypeError):
                                errors_list.append(f'Row {index + 2}, Column {column_letter} ({excel_field}): Value has invalid format')

                    # Optional fields
                    optional_fields = [
                        ('Source Landmark', 'src_landmark', str),
                        ('Destination Landmark', 'dst_landmark', str),
                        ('Remarks', 'remarks', str)
                    ]

                    for excel_field, db_field, field_type in optional_fields:
                        value = row.get(excel_field)
                        if not pd.isna(value):
                            lane[db_field] = str(value)

                    # Process stop points
                    for i in range(1, 6):  # For each of the 5 stop points
                        # Check if stop point type is provided
                        stop_point_type = row.get(f'Stop Point {i} Type')

                        if not pd.isna(stop_point_type):
                            # Create a stop point object
                            stop_point = {
                                'id': get_uuid(),
                                'point_type': str(stop_point_type),
                                'created_on': request.now,
                                'updated_on': request.now
                            }

                            # Process stop point fields
                            stop_point_fields = [
                                (f'Stop Point {i} Address', 'address', str, True),
                                (f'Stop Point {i} Landmark', 'landmark', str, False),
                                (f'Stop Point {i} Pincode', 'pincode', str, True),
                                (f'Stop Point {i} City', 'city', str, True),
                                (f'Stop Point {i} State', 'state', str, True),
                                (f'Stop Point {i} State Code', 'state_code', str, True)
                            ]

                            stop_point_valid = True

                            for excel_field, db_field, field_type, required in stop_point_fields:
                                value = row.get(excel_field)
                                column_letter = column_mapping.get(excel_field, '?')
                                if required and pd.isna(value):
                                    errors_list.append(f'Row {index + 2}, Column {column_letter} ({excel_field}): Value is required for Stop Point {i}')
                                    stop_point_valid = False
                                elif not pd.isna(value):
                                    stop_point[db_field] = str(value)

                            # Add stop point to lane if all required fields are provided
                            if stop_point_valid:
                                lane['stop_points'].append(stop_point)

                    # Validate lane using LaneSchema
                    try:
                        # Validate stop points using StopPointSchema
                        validated_stop_points = []
                        for stop_point in lane['stop_points']:
                            try:
                                from schema import StopPointSchema
                                validated_stop_point = StopPointSchema(**stop_point).model_dump()
                                validated_stop_points.append(validated_stop_point)
                            except ValidationError as e:
                                stop_point_errors = e.errors()
                                for error in stop_point_errors:
                                    field_name = error['loc'][0]
                                    # Map the field name back to Excel column if possible
                                    excel_field = f'Stop Point {i} {field_name.capitalize()}'
                                    column_letter = column_mapping.get(excel_field, '?')
                                    errors_list.append(f"Row {index + 2}, Column {column_letter} ({excel_field}): {error['msg']}")

                        # Replace stop_points with validated ones
                        lane['stop_points'] = validated_stop_points

                        # Calculate distance and TAT for the lane
                        try:
                            # Only calculate if there are no errors so far
                            if len(errors_list) == 0:
                                # Convert pincode fields to integers for the calculation
                                if 'src_pincode' in lane and lane['src_pincode']:
                                    lane['src_pincode'] = int(float(lane['src_pincode']))
                                if 'dst_pincode' in lane and lane['dst_pincode']:
                                    lane['dst_pincode'] = int(float(lane['dst_pincode']))
                                for stop_point in lane['stop_points']:
                                    if 'pincode' in stop_point and stop_point['pincode']:
                                        stop_point['pincode'] = int(float(stop_point['pincode']))

                                # Calculate distance and TAT
                                calculation_success = self._calculate_distance_and_tat(lane)

                                if not calculation_success:
                                    logger.warning(f"Row {index + 2}: Could not calculate distance and TAT for given pincodes.")
                        except Exception as e:
                            logger.error(f"Row {index + 2}: Please verfiy all pincodes. Error in distance calculation - {str(e)}")
                            # Non-blocking error - continue with validation

                        # Validate the entire lane
                        from schema import LaneSchema
                        validated_lane = LaneSchema(**lane).model_dump()

                        # Add lane to lanes list if no errors
                        if len(errors_list) == 0:
                            lanes.append(validated_lane)
                    except ValidationError as e:
                        lane_errors = e.errors()
                        for error in lane_errors:
                            field_name = error['loc'][0]
                            # Try to map the field name back to Excel column
                            excel_field = None
                            for ef, df, _ in required_fields + optional_fields:
                                if df == field_name:
                                    excel_field = ef
                                    break

                            # For stop points, handle differently
                            if field_name == 'stop_points' and len(error['loc']) > 1:
                                stop_point_idx = error['loc'][1]
                                if len(error['loc']) > 2:
                                    stop_point_field = error['loc'][2]
                                    excel_field = f'Stop Point {stop_point_idx + 1} {stop_point_field.capitalize()}'
                                else:
                                    excel_field = f'Stop Point {stop_point_idx + 1} Type'

                            column_letter = column_mapping.get(excel_field, '?') if excel_field else '?'
                            field_display = excel_field if excel_field else field_name
                            errors_list.append(f"Row {index + 2}, Column {column_letter} ({field_display}): {error['msg']}")

                except Exception as e:
                    # Try to identify which column might have caused the error
                    error_msg = str(e)
                    error_column = '?'
                    error_field = 'Unknown field'

                    # Check if error message contains any field names
                    for ef, _, _ in required_fields + optional_fields:
                        if ef.lower() in error_msg.lower():
                            error_field = ef
                            error_column = column_mapping.get(ef, '?')
                            break

                    errors_list.append(f'Row {index + 2}, Column {error_column} ({error_field}): {error_msg}')

            # If there are errors, return them
            if len(errors_list) > 0:
                return format_error_response(status.HTTP_400_BAD_REQUEST, 'Failed to process data', errors_list)

            # If no lanes were found, return an error
            if len(lanes) == 0:
                return format_error_response(status.HTTP_400_BAD_REQUEST, 'No valid lanes found in the file')

            # Save lanes to database
            db.insert(ProcurementDBColls.LANES, lanes, insert_many=True)

            # Return success response
            return format_response(status.HTTP_200_OK, {'lanes_created': len(lanes)}, 'Lanes uploaded successfully')

        except Exception as e:
            logger.error(f'Error processing lanes upload: {str(e)}')
            return format_error_response(status.HTTP_500_INTERNAL_SERVER_ERROR, f'Error processing file: {str(e)}')


class DownloadLanesTemplateAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.VIEW, ['GET']),
    ]

    def get(self, request, *args, **kwargs):
        # Create an in-memory Excel file
        output = BytesIO()
        # Set options to ensure the first sheet is active when opened
        workbook = xlsxwriter.Workbook(output, {
            'in_memory': True,
            'first_sheet': 0,  # Make the first sheet active
            'strings_to_urls': False  # Disable automatic URL detection for better performance
        })

        # Fetch reference data from database
        vehicle_types = Memoization.get_dropdown_data(AdminDBColls.VEHICLE_TYPES)
        body_types = Memoization.get_dropdown_data(AdminDBColls.VEHICLE_BODY_TYPES)
        states = Memoization.get_dropdown_data(AdminDBColls.STATES_LIST)
        business_volume_uoms = Memoization.get_dropdown_data(AdminDBColls.BUSINESS_VOLUME_UOMS)
        stop_point_types = [{'id': x.value, 'name': x.value} for x in StopPointType]

        # Add formatting
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D3D3D3',
            'border': 1
        })

        required_format = workbook.add_format({
            'bold': True,
            'bg_color': '#FFCCCB',  # Light red for required fields
            'border': 1
        })

        # Add a format for the data entry headers
        data_entry_header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4F81BD',  # Blue header
            'font_color': 'white',
            'border': 1,
            'text_wrap': True,
            'align': 'center',
            'valign': 'vcenter',
            'locked': True  # Lock header cells
        })

        # Create the main data entry worksheet first (this will be the active sheet)
        data_worksheet = workbook.add_worksheet("Lanes")

        # Create a reference data worksheet with a non-obvious name (hidden and protected)
        reference_worksheet = workbook.add_worksheet("_SYS_DATA_REF_DO_NOT_MODIFY")

        # Hide the reference worksheet
        # Note: xlsxwriter's hide() method only supports standard hiding (not 'very hidden')
        # We'll compensate with additional protection measures
        reference_worksheet.hide()

        # Explicitly activate the data worksheet
        data_worksheet.activate()

        # Protect the worksheet with a strong password to prevent users from modifying it
        reference_worksheet.protect('SystemDataProtection2023!', {
            'format_cells': False,
            'format_columns': True,  # Allow column formatting/resizing
            'format_rows': True,     # Allow row formatting/resizing
            'insert_columns': False,
            'insert_rows': False,
            'insert_hyperlinks': False,
            'delete_columns': False,
            'delete_rows': False,
            'select_locked_cells': False,
            'sort': False,
            'autofilter': False,
            'pivot_tables': False,
            'objects': False,
            'scenarios': False
        })

        # Freeze the reference data to prevent changes
        reference_worksheet.freeze_panes(1, 0)

        # Populate reference data worksheet with vehicle types
        reference_worksheet.write(0, 0, "vehicle_type_id", header_format)
        reference_worksheet.write(0, 1, "vehicle_type", header_format)
        for i, vt in enumerate(vehicle_types, start=1):
            reference_worksheet.write(i, 0, vt.get('id'))
            reference_worksheet.write(i, 1, vt.get('name', ''))

        # Populate reference data worksheet with body types
        reference_worksheet.write(0, 3, "body_type_id", header_format)
        reference_worksheet.write(0, 4, "body_type", header_format)
        for i, bt in enumerate(body_types, start=1):
            reference_worksheet.write(i, 3, bt.get('id'))
            reference_worksheet.write(i, 4, bt.get('name', ''))

        # Populate reference data worksheet with states
        reference_worksheet.write(0, 6, "state_code", header_format)
        reference_worksheet.write(0, 7, "state", header_format)
        for i, state in enumerate(states, start=1):
            reference_worksheet.write(i, 6, state.get('id'))
            reference_worksheet.write(i, 7, state.get('name', ''))

        # Populate reference data worksheet with volume UOM
        reference_worksheet.write(0, 9, "volume_uom_id", header_format)
        reference_worksheet.write(0, 10, "volume_uom", header_format)
        for i, vol_uom in enumerate(business_volume_uoms, start=1):
            reference_worksheet.write(i, 9, vol_uom.get('id'))
            reference_worksheet.write(i, 10, vol_uom.get('name', ''))

        # Populate reference data worksheet with StopPointType enum values
        reference_worksheet.write(0, 12, "stop_point_type_id", header_format)
        reference_worksheet.write(0, 13, "stop_point_type", header_format)
        # Add the two values from StopPointType enum: 'Drop' and 'Pickup'
        for i, point_type in enumerate(stop_point_types, start=1):
            reference_worksheet.write(i, 12, point_type.get('id'))
            reference_worksheet.write(i, 13, point_type.get('name', ''))

        # Create a format for unlocked cells (data entry)
        unlocked_format = workbook.add_format({
            'locked': False,
            'border': 1
        })

        # By default, protect the worksheet but allow users to select locked cells and resize columns
        data_worksheet.protect('', {
            'format_cells': True,
            'format_columns': True,
            'format_rows': True,
            'insert_columns': False,
            'insert_rows': True,
            'insert_hyperlinks': False,
            'delete_columns': False,
            'delete_rows': True,
            'select_locked_cells': True,
            'sort': True,
            'autofilter': True,
            'pivot_tables': False,
            'objects': False,
            'scenarios': False
        })

        # Freeze the header row
        data_worksheet.freeze_panes(1, 0)

        # Define headers for the lanes worksheet
        lane_headers = [
            {
                'name': 'placement_date', 'display_name': 'Placement Date', 'required': False, 'description': 'Vehicle placement date in DD/MM/YY format',
                'validation': {
                    'type': 'date', 'criteria': 'between', 'minimum': '=TODAY()', 'maximum': '=TODAY()+30', 'error_title': 'Invalid Date',
                    'error_message': 'Please enter a date between today and one month from today in DD/MM/YY format.'
                }
            },
            {
                'name': 'placement_time', 'display_name': 'Placement Time', 'required': False, 'description': 'Vehicle placement time in 24-hour format (e.g., 14:30)',
                'validation': {
                    'type': 'time', 'criteria': 'between', 'minimum': 0, 'maximum': 0.9993055555556, 'error_title': 'Invalid Time',
                    'error_message': 'Please enter a time in 24-hour format (HH:MM).'
                }
            },
            {
                'name': 'no_of_vehicles', 'display_name': 'Number of Vehicles', 'required': True, 'description': 'Number of vehicles required',
                'validation': {
                    'type': 'integer', 'criteria': 'between', 'minimum': 1, 'maximum': 1000, 'error_title': 'Invalid Number',
                    'error_message': 'Please enter a positive whole number between 1 and 1000.'
                }
            },
            {'name': 'vehicle_type', 'display_name': 'Vehicle Type', 'required': True, 'description': 'Type of vehicle', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$B$2:$B$' + str(len(vehicle_types) + 1)},
            {'name': 'vehicle_type_id', 'display_name': 'Vehicle Type ID', 'required': True, 'description': 'ID of the vehicle type (auto-populated)', 'hidden': True},
            {
                'name': 'vehicle_capacity', 'display_name': 'Vehicle Capacity (MT)', 'required': True, 'description': 'Vehicle capacity in MT',
                'validation': {
                    'type': 'decimal', 'criteria': 'greater than', 'value': 0, 'error_title': 'Invalid Vehicle Capacity',
                    'error_message': 'Please enter a positive number for Vehicle Capacity.'
                }
            },
            {'name': 'body_type', 'display_name': 'Vehicle Body Type', 'required': True, 'description': 'Type of vehicle body', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$E$2:$E$' + str(len(body_types) + 1)},
            {'name': 'body_type_id', 'display_name': 'Vehicle Body Type ID', 'required': True, 'description': 'ID of the body type (auto-populated)', 'hidden': True},
            {'name': 'src_address', 'display_name': 'Source Address', 'required': True, 'description': 'Source address'},
            {'name': 'src_city', 'display_name': 'Source City', 'required': True, 'description': 'Source city'},
            {'name': 'src_state', 'display_name': 'Source State', 'required': True, 'description': 'Source state', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$H$2:$H$' + str(len(states) + 1)},
            {'name': 'src_state_code', 'display_name': 'Source State Code', 'required': True, 'description': 'Source state code (auto-populated)', 'hidden': True},
            {
                'name': 'src_pincode', 'display_name': 'Source Pincode', 'required': True, 'description': 'Source pincode (6-digit Indian pincode)',
                'validation': {
                    'type': 'integer', 'criteria': 'between', 'minimum': 100000, 'maximum': 999999, 'error_title': 'Invalid Pincode',
                    'error_message': 'Please enter a valid 6-digit Indian pincode.'
                }
            },
            {'name': 'src_landmark', 'display_name': 'Source Landmark', 'required': False, 'description': 'Source landmark'},
            {'name': 'dst_address', 'display_name': 'Destination Address', 'required': True, 'description': 'Destination address'},
            {'name': 'dst_city', 'display_name': 'Destination City', 'required': True, 'description': 'Destination city'},
            {'name': 'dst_state', 'display_name': 'Destination State', 'required': True, 'description': 'Destination state', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$H$2:$H$' + str(len(states) + 1)},
            {'name': 'dst_state_code', 'display_name': 'Destination State Code', 'required': True, 'description': 'Destination state code (auto-populated)', 'hidden': True},
            {
                'name': 'dst_pincode', 'display_name': 'Destination Pincode', 'required': True, 'description': 'Destination pincode (6-digit Indian pincode)',
                'validation': {
                    'type': 'integer', 'criteria': 'between', 'minimum': 100000, 'maximum': 999999, 'error_title': 'Invalid Pincode',
                    'error_message': 'Please enter a valid 6-digit Indian pincode.'
                }
            },
            {'name': 'dst_landmark', 'display_name': 'Destination Landmark', 'required': False, 'description': 'Destination landmark'},
            {
                'name': 'rate', 'display_name': 'Ceiling Price', 'required': True, 'description': 'Ceiling Price for bidding',
                'validation': {
                    'type': 'integer', 'criteria': 'between', 'minimum': 100, 'maximum': 20000, 'error_title': 'Invalid Price',
                    'error_message': 'Please enter a positive number for Ceiling Price.'
                }
            },
            {
                'name': 'volume', 'display_name': 'Business Volume', 'required': True, 'description': 'Business Volume of goods',
                'validation': {
                    'type': 'decimal', 'criteria': 'greater than', 'value': 0, 'error_title': 'Invalid Volume',
                    'error_message': 'Please enter a positive number for Business Volume.'
                }
            },
            {'name': 'volume_uom', 'display_name': 'Business Volume UOM', 'required': True, 'description': 'Unit of measure of businesss volume', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$K$2:$K$' + str(len(business_volume_uoms) + 1)},
            {'name': 'volume_uom_id', 'display_name': 'Business Volume UOM ID', 'required': True, 'description': 'ID of businesss volume unit of measure (auto-populated)', 'hidden': True},
            {'name': 'remarks', 'display_name': 'Remarks', 'required': False, 'description': 'Additional remarks'},

            # Stop Point 1
            {'name': 'stop_point_type_1', 'display_name': 'Stop Point 1 Type', 'required': False, 'description': 'Type of stop point 1 (Pickup or Drop)', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$N$2:$N$' + str(len(stop_point_types) + 1)},
            {'name': 'stop_point_address_1', 'display_name': 'Stop Point 1 Address', 'required': False, 'description': 'Address of stop point 1'},
            {'name': 'stop_point_city_1', 'display_name': 'Stop Point 1 City', 'required': False, 'description': 'City of stop point 1'},
            {'name': 'stop_point_state_1', 'display_name': 'Stop Point 1 State', 'required': False, 'description': 'State of stop point 1', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$H$2:$H$' + str(len(states) + 1)},
            {'name': 'stop_point_state_code_1', 'display_name': 'Stop Point 1 State Code', 'required': False, 'description': 'State code of stop point 1 (auto-populated)', 'hidden': True},
            {
                'name': 'stop_point_pincode_1', 'display_name': 'Stop Point 1 Pincode', 'required': False, 'description': 'Pincode of stop point 1 (6-digit Indian pincode)',
                'validation': {
                    'type': 'integer', 'criteria': 'between', 'minimum': 100000, 'maximum': 999999, 'error_title': 'Invalid Pincode',
                    'error_message': 'Please enter a valid 6-digit Indian pincode.'
                }
            },
            {'name': 'stop_point_landmark_1', 'display_name': 'Stop Point 1 Landmark', 'required': False, 'description': 'Landmark of stop point 1'},

            # Stop Point 2
            {'name': 'stop_point_type_2', 'display_name': 'Stop Point 2 Type', 'required': False, 'description': 'Type of stop point 2 (Pickup or Drop)', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$N$2:$N$' + str(len(stop_point_types) + 1)},
            {'name': 'stop_point_address_2', 'display_name': 'Stop Point 2 Address', 'required': False, 'description': 'Address of stop point 2'},
            {'name': 'stop_point_city_2', 'display_name': 'Stop Point 2 City', 'required': False, 'description': 'City of stop point 2'},
            {'name': 'stop_point_state_2', 'display_name': 'Stop Point 2 State', 'required': False, 'description': 'State of stop point 2', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$H$2:$H$' + str(len(states) + 1)},
            {'name': 'stop_point_state_code_2', 'display_name': 'Stop Point 2 State Code', 'required': False, 'description': 'State code of stop point 2 (auto-populated)', 'hidden': True},
            {
                'name': 'stop_point_pincode_2', 'display_name': 'Stop Point 2 Pincode', 'required': False, 'description': 'Pincode of stop point 2 (6-digit Indian pincode)',
                'validation': {
                    'type': 'integer', 'criteria': 'between', 'minimum': 100000, 'maximum': 999999, 'error_title': 'Invalid Pincode',
                    'error_message': 'Please enter a valid 6-digit Indian pincode.'
                }
            },
            {'name': 'stop_point_landmark_2', 'display_name': 'Stop Point 2 Landmark', 'required': False, 'description': 'Landmark of stop point 2'},

            # Stop Point 3
            {'name': 'stop_point_type_3', 'display_name': 'Stop Point 3 Type', 'required': False, 'description': 'Type of stop point 3 (Pickup or Drop)', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$N$2:$N$' + str(len(stop_point_types) + 1)},
            {'name': 'stop_point_address_3', 'display_name': 'Stop Point 3 Address', 'required': False, 'description': 'Address of stop point 3'},
            {'name': 'stop_point_city_3', 'display_name': 'Stop Point 3 City', 'required': False, 'description': 'City of stop point 3'},
            {'name': 'stop_point_state_3', 'display_name': 'Stop Point 3 State', 'required': False, 'description': 'State of stop point 3', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$H$2:$H$' + str(len(states) + 1)},
            {'name': 'stop_point_state_code_3', 'display_name': 'Stop Point 3 State Code', 'required': False, 'description': 'State code of stop point 3 (auto-populated)', 'hidden': True},
            {
                'name': 'stop_point_pincode_3', 'display_name': 'Stop Point 3 Pincode', 'required': False, 'description': 'Pincode of stop point 3 (6-digit Indian pincode)',
                'validation': {
                    'type': 'integer', 'criteria': 'between', 'minimum': 100000, 'maximum': 999999, 'error_title': 'Invalid Pincode',
                    'error_message': 'Please enter a valid 6-digit Indian pincode.'
                }
            },
            {'name': 'stop_point_landmark_3', 'display_name': 'Stop Point 3 Landmark', 'required': False, 'description': 'Landmark of stop point 3'},

            # Stop Point 4
            {'name': 'stop_point_type_4', 'display_name': 'Stop Point 4 Type', 'required': False, 'description': 'Type of stop point 4 (Pickup or Drop)', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$N$2:$N$' + str(len(stop_point_types) + 1)},
            {'name': 'stop_point_address_4', 'display_name': 'Stop Point 4 Address', 'required': False, 'description': 'Address of stop point 4'},
            {'name': 'stop_point_city_4', 'display_name': 'Stop Point 4 City', 'required': False, 'description': 'City of stop point 4'},
            {'name': 'stop_point_state_4', 'display_name': 'Stop Point 4 State', 'required': False, 'description': 'State of stop point 4', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$H$2:$H$' + str(len(states) + 1)},
            {'name': 'stop_point_state_code_4', 'display_name': 'Stop Point 4 State Code', 'required': False, 'description': 'State code of stop point 4 (auto-populated)', 'hidden': True},
            {
                'name': 'stop_point_pincode_4', 'display_name': 'Stop Point 4 Pincode', 'required': False, 'description': 'Pincode of stop point 4 (6-digit Indian pincode)',
                'validation': {
                    'type': 'integer', 'criteria': 'between', 'minimum': 100000, 'maximum': 999999, 'error_title': 'Invalid Pincode',
                    'error_message': 'Please enter a valid 6-digit Indian pincode.'
                }
            },
            {'name': 'stop_point_landmark_4', 'display_name': 'Stop Point 4 Landmark', 'required': False, 'description': 'Landmark of stop point 4'},

            # Stop Point 5
            {'name': 'stop_point_type_5', 'display_name': 'Stop Point 5 Type', 'required': False, 'description': 'Type of stop point 5 (Pickup or Drop)', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$N$2:$N$' + str(len(stop_point_types) + 1)},
            {'name': 'stop_point_address_5', 'display_name': 'Stop Point 5 Address', 'required': False, 'description': 'Address of stop point 5'},
            {'name': 'stop_point_city_5', 'display_name': 'Stop Point 5 City', 'required': False, 'description': 'City of stop point 5'},
            {'name': 'stop_point_state_5', 'display_name': 'Stop Point 5 State', 'required': False, 'description': 'State of stop point 5', 'dropdown': True, 'dropdown_source': '=_SYS_DATA_REF_DO_NOT_MODIFY!$H$2:$H$' + str(len(states) + 1)},
            {'name': 'stop_point_state_code_5', 'display_name': 'Stop Point 5 State Code', 'required': False, 'description': 'State code of stop point 5 (auto-populated)', 'hidden': True},
            {
                'name': 'stop_point_pincode_5', 'display_name': 'Stop Point 5 Pincode', 'required': False, 'description': 'Pincode of stop point 5 (6-digit Indian pincode)',
                'validation': {
                    'type': 'integer', 'criteria': 'between', 'minimum': 100000, 'maximum': 999999, 'error_title': 'Invalid Pincode',
                    'error_message': 'Please enter a valid 6-digit Indian pincode.'
                }
            },
            {'name': 'stop_point_landmark_5', 'display_name': 'Stop Point 5 Landmark', 'required': False, 'description': 'Landmark of stop point 5'}
        ]

        # Write headers to the data worksheet
        visible_col = 0
        col_indices = {}
        for col_num, header in enumerate(lane_headers):
            if header.get('hidden'):
                # Hide the column in the data entry sheet
                data_worksheet.set_column(visible_col, visible_col, None, None, {'hidden': True})
            # Use display_name for the header if available, otherwise fall back to name
            header_text = header.get('display_name', header['name'])
            data_worksheet.write(0, visible_col, header_text, data_entry_header_format)
            col_indices[header['name']] = visible_col
            visible_col += 1

        # Add data validation for dropdown fields and apply unlocked format to data cells
        for row in range(1, MAX_LANES_PER_TEMPLATE + 1):  # Allow up to 100 lanes
            for header in lane_headers:
                col_index = col_indices[header['name']]
                # Apply unlocked format to all data cells
                data_worksheet.write(row, col_index, None, unlocked_format)

                # Add dropdown validation where needed
                if header.get('dropdown'):
                    data_worksheet.data_validation(row, col_index, row, col_index, {
                        'validate': 'list',
                        'source': header['dropdown_source'],
                        'input_title': header['display_name'],
                        'input_message': header['description'],
                    })
                # Add date validation if specified
                elif header.get('validation') and header['validation']['type'] == 'date':
                    validation = header['validation']
                    data_worksheet.data_validation(row, col_index, row, col_index, {
                        'validate': 'date',
                        'criteria': validation['criteria'],
                        'minimum': validation['minimum'],
                        'maximum': validation['maximum'],
                        'error_title': validation['error_title'],
                        'error_message': validation['error_message'],
                        'error_type': 'stop',
                        'input_title': header['display_name'],
                        'input_message': header['description'],
                    })
                # Add time validation if specified
                elif header.get('validation') and header['validation']['type'] == 'time':
                    validation = header['validation']
                    data_worksheet.data_validation(row, col_index, row, col_index, {
                        'validate': 'time',
                        'criteria': validation['criteria'],
                        'minimum': validation['minimum'],
                        'maximum': validation['maximum'],
                        'error_title': validation['error_title'],
                        'error_message': validation['error_message'],
                        'error_type': 'stop',
                        'input_title': header['display_name'],
                        'input_message': header['description'],
                    })
                # Add integer validation
                elif header.get('validation') and header['validation']['type'] == 'integer':
                    validation = header['validation']
                    data_worksheet.data_validation(row, col_index, row, col_index, {
                        'validate': 'integer',
                        'criteria': validation['criteria'],
                        'minimum': validation.get('minimum'),
                        'maximum': validation.get('maximum'),
                        'value': validation.get('value'),
                        'error_title': validation['error_title'],
                        'error_message': validation['error_message'],
                        'error_type': 'stop',
                        'input_title': header['display_name'],
                        'input_message': header['description'],
                    })
                # # Add decimal validation
                elif header.get('validation') and header['validation']['type'] == 'decimal':
                    validation = header['validation']
                    data_worksheet.data_validation(row, col_index, row, col_index, {
                        'validate': 'decimal',
                        'criteria': validation['criteria'],
                        # 'minimum': validation.get('minimum'),
                        # 'maximum': validation.get('maximum'),
                        'value': validation.get('value'),
                        'error_title': validation['error_title'],
                        'error_message': validation['error_message'],
                        'error_type': 'stop',
                        'input_title': header['display_name'],
                        'input_message': header['description'],
                    })
                else:
                    data_worksheet.data_validation(row, col_index, row, col_index, {
                        'validate': 'any',
                        'input_title': header['display_name'],
                        'input_message': header['description'],
                        'ignore_blank': True,
                    })

        # Add formulas to lookup IDs based on selected names
        for row in range(1, MAX_LANES_PER_TEMPLATE + 1):  # Allow up to 100 lanes
            # Formula for vehicle_type_id
            vehicle_type_col = col_indices['vehicle_type'] + 1
            vehicle_type_id_col = col_indices['vehicle_type_id']
            vehicle_type_formula = (
                f'=XLOOKUP(INDIRECT("R{row+1}C{vehicle_type_col}", FALSE), '
                f'_SYS_DATA_REF_DO_NOT_MODIFY!$B$2:$B${len(vehicle_types)+1}, '
                f'_SYS_DATA_REF_DO_NOT_MODIFY!$A$2:$A${len(vehicle_types)+1}, '
                f'NA())'
            )
            data_worksheet.write_formula(row, vehicle_type_id_col, vehicle_type_formula)

            # Formula for body_type_id
            body_type_col = col_indices['body_type'] + 1
            body_type_id_col = col_indices['body_type_id']
            body_type_formula = (
                f'=XLOOKUP(INDIRECT("R{row+1}C{body_type_col}", FALSE), '
                f'_SYS_DATA_REF_DO_NOT_MODIFY!$E$2:$E${len(body_types)+1}, '
                f'_SYS_DATA_REF_DO_NOT_MODIFY!$D$2:$D${len(body_types)+1}, '
                f'NA())'
            )
            data_worksheet.write_formula(row, body_type_id_col, body_type_formula)

            # Formula for src_state_code
            src_state_col = col_indices['src_state'] + 1
            src_state_code_col = col_indices['src_state_code']
            src_state_formula = (
                f'=XLOOKUP(INDIRECT("R{row+1}C{src_state_col}", FALSE), '
                f'_SYS_DATA_REF_DO_NOT_MODIFY!$H$2:$H${len(states)+1}, '
                f'_SYS_DATA_REF_DO_NOT_MODIFY!$G$2:$G${len(states)+1}, '
                f'NA())'
            )
            data_worksheet.write_formula(row, src_state_code_col, src_state_formula)

            # Formula for dst_state_code
            dst_state_col = col_indices['dst_state'] + 1
            dst_state_code_col = col_indices['dst_state_code']
            dst_state_formula = (
                f'=XLOOKUP(INDIRECT("R{row+1}C{dst_state_col}", FALSE), '
                f'_SYS_DATA_REF_DO_NOT_MODIFY!$H$2:$H${len(states)+1}, '
                f'_SYS_DATA_REF_DO_NOT_MODIFY!$G$2:$G${len(states)+1}, '
                f'NA())'
            )
            data_worksheet.write_formula(row, dst_state_code_col, dst_state_formula)

            # Formulas for stop point state codes
            for i in range(1, 6):  # For each of the 5 stop points
                stop_point_state_col = col_indices[f'stop_point_state_{i}'] + 1
                stop_point_state_code_col = col_indices[f'stop_point_state_code_{i}']
                stop_point_state_formula = (
                    f'=XLOOKUP(INDIRECT("R{row+1}C{stop_point_state_col}", FALSE), '
                    f'_SYS_DATA_REF_DO_NOT_MODIFY!$H$2:$H${len(states)+1}, '
                    f'_SYS_DATA_REF_DO_NOT_MODIFY!$G$2:$G${len(states)+1}, '
                    f'NA())'
                )
                data_worksheet.write_formula(row, stop_point_state_code_col, stop_point_state_formula)

            # Formula for volume_uom_id
            volume_uom_col = col_indices['volume_uom'] + 1
            volume_uom_id_col = col_indices['volume_uom_id']
            volume_uom_formula = (
                f'=XLOOKUP(INDIRECT("R{row+1}C{volume_uom_col}", FALSE), '
                f'_SYS_DATA_REF_DO_NOT_MODIFY!$K$2:$K${len(business_volume_uoms)+1}, '
                f'_SYS_DATA_REF_DO_NOT_MODIFY!$J$2:$J${len(business_volume_uoms)+1}, '
                f'NA())'
            )
            data_worksheet.write_formula(row, volume_uom_id_col, volume_uom_formula)

        # Set column widths in the data entry sheet for better readability
        for i, header in enumerate(lane_headers):
            if not header.get('hidden'):
                data_worksheet.set_column(i, i, 20)  # Set width to 20 for all visible columns

        # Create a documentation worksheet
        docs_worksheet = workbook.add_worksheet("Documentation")

        # Write headers to the documentation worksheet
        docs_worksheet.write(0, 0, 'Column Name', header_format)
        docs_worksheet.write(0, 1, 'Required', header_format)
        docs_worksheet.write(0, 2, 'Description', header_format)
        # docs_worksheet.write(0, 3, 'Database Field', header_format)

        # Write lane headers and descriptions to documentation
        for row_num, header in enumerate(lane_headers, start=1):
            format_to_use = required_format if header['required'] else header_format
            # Use display_name for the header if available, otherwise fall back to name
            header_text = header.get('display_name', header['name'])
            docs_worksheet.write(row_num, 0, header_text, format_to_use)
            # Also include the database field name for reference
            docs_worksheet.write(row_num, 1, 'Yes' if header['required'] else 'No', format_to_use if header['required'] else header_format)
            docs_worksheet.write(row_num, 2, header['description'], header_format)
            # docs_worksheet.write(row_num, 3, header['name'], header_format)

        # Set column widths for better readability
        docs_worksheet.set_column(0, 0, 25)  # Display name column
        docs_worksheet.set_column(1, 1, 10)  # Required column
        docs_worksheet.set_column(2, 2, 50)  # Description column
        # docs_worksheet.set_column(3, 3, 20)  # Database field column

        # Protect the documentation worksheet but allow column resizing
        docs_worksheet.protect('', {
            'format_cells': False,
            'format_columns': True,  # Allow column formatting/resizing
            'format_rows': True,     # Allow row formatting/resizing
            'insert_columns': False,
            'insert_rows': False,
            'insert_hyperlinks': False,
            'delete_columns': False,
            'delete_rows': False,
            'select_locked_cells': True,
            'sort': False,
            'autofilter': False,
            'pivot_tables': False,
            'objects': False,
            'scenarios': False
        })

        # Freeze the header row in documentation worksheet
        docs_worksheet.freeze_panes(1, 0)

        # Create an instructions worksheet
        instructions_worksheet = workbook.add_worksheet("Instructions")

        # Write instructions
        instructions = [
            "Instructions for using this template:",
            "",
            "1. Use the 'Lanes' sheet to enter your lane data.",
            "2. Fields marked as 'Required: Yes' in the Documentation sheet must be filled in.",
            "3. Vehicle Type, Body Type, States and Volume UOM fields have dropdowns with valid values from the system.",
            "4. The ID fields will be auto-populated based on your dropdown selections - do not modify them.",
            "5. For Placement Date, enter a date in DD/MM/YY format. The date must be between today and one year from today.",
            "6. For Placement Time, enter a time in 24-hour format (HH:MM).",
            "7. All state codes should be 2 letters (e.g., 'MH' for Maharashtra).",
            "8. All pincode fields require a valid 6-digit Indian pincode (100000-999999).",
            "9. Number of Vehicles must be a positive whole number between 1 and 1000.",
            "10. Rate and Volume fields must be positive numbers.",
            "11. You can add up to 5 stop points for each lane. For each stop point, specify the type (Pickup or Drop), address, and other details.",
            "12. For Stop Point Type, select either 'Pickup' or 'Drop' from the dropdown list.",
            "13. For Stop Point State, select from the dropdown list to auto-populate the state code.",
            "14. The template is protected to prevent accidental changes to the structure and formulas.",
            "15. The reference data sheet is hidden and protected - this is by design to ensure data integrity.",
            "16. Only enter data in the designated cells in the 'Lanes' sheet.",
            "17. User-friendly field names are displayed in the template, but the Documentation sheet shows both the display name and the corresponding database field name.",
            "18. You can resize columns in all sheets if needed for better visibility.",
            "19. Save this file as Excel (.xlsx) before uploading.",
            "20. For any issues or questions, please contact support."
        ]

        for row_num, instruction in enumerate(instructions):
            instructions_worksheet.write(row_num, 0, instruction)

        # Set column width for instructions
        instructions_worksheet.set_column(0, 0, 100)

        # Protect the instructions worksheet but allow column resizing
        instructions_worksheet.protect('', {
            'format_cells': False,
            'format_columns': True,  # Allow column formatting/resizing
            'format_rows': True,     # Allow row formatting/resizing
            'insert_columns': False,
            'insert_rows': False,
            'insert_hyperlinks': False,
            'delete_columns': False,
            'delete_rows': False,
            'select_locked_cells': True,
            'sort': False,
            'autofilter': False,
            'pivot_tables': False,
            'objects': False,
            'scenarios': False
        })

        # Finalize the workbook
        workbook.close()
        output.seek(0)

        # Create HTTP response for file download
        response = HttpResponse(output.read(), content_type=LongMIMETypes.SHEET)
        response['Content-Disposition'] = 'attachment; filename="lanes_upload_template.xlsx"'
        return response


@method_decorator(validate_request_payload(CreateUpdateStopPointPayloadValidator), name='post')
@method_decorator(validate_request_payload(CreateUpdateStopPointPayloadValidator), name='put')
class StopPointCRUDAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.CREATE_STOP_POINT, ['POST']),
        (Permissions.Procurement.UPDATE_STOP_POINT, ['PUT']),
        (Permissions.Procurement.DELETE_STOP_POINT, ['DELETE']),
    ]

    @validate_data_access(['rfq'])
    def post(self, request, *args, **kwargs):
        rfq_id = kwargs['rfq_id']
        lane_id = kwargs['lane_id']
        payload = request.validated_payload.model_dump()

        db = MongoUtility()

        lane_query = {'id': lane_id, 'rfq_id': rfq_id, 'company_id': request.company_id}
        lane_doc = db.find(ProcurementDBColls.LANES, lane_query, find_one=True)
        if not lane_doc:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Please save the Lane before adding Stop Points.')

        push_query = {'stop_points': {'$each': [payload]}}
        set_query = {'updated_on': request.now}
        db.update(ProcurementDBColls.LANES, lane_query, set_query=set_query, push_query=push_query)

        response_data = payload
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    @validate_data_access(['rfq', 'lane'])
    def put(self, request, *args, **kwargs):
        rfq_id = kwargs['rfq_id']
        lane_id = kwargs['lane_id']
        stop_point_id = kwargs['stop_point_id']
        payload = request.validated_payload.model_dump()

        db = MongoUtility()

        lane_query = {'id': lane_id, 'rfq_id': rfq_id, 'company_id': request.company_id}

        payload['id'] = stop_point_id

        lane_query['stop_points.id'] = stop_point_id
        updated_query = {
            'stop_points.$': payload,
            'updated_on': request.now
        }
        db.update(ProcurementDBColls.LANES, lane_query, updated_query)

        response_data = payload
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    @validate_data_access(['rfq', 'lane'])
    def delete(self, request, *args, **kwargs):
        rfq_id = kwargs['rfq_id']
        lane_id = kwargs['lane_id']
        stop_point_id = kwargs['stop_point_id']

        db = MongoUtility()

        lane_query = {'id': lane_id, 'rfq_id': rfq_id, 'company_id': request.company_id}

        pull_query = {'stop_points': {'id': stop_point_id}}
        set_query = {'updated_on': request.now}
        db.update(ProcurementDBColls.LANES, lane_query, set_query=set_query, pull_query=pull_query)

        return format_response(status.HTTP_200_OK, {}, 'Success')
