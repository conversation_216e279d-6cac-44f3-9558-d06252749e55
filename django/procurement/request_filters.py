import logging
from utils.mongo import MongoUtility
from utils.constants import ProcurementDBColls, RFQStatus

logger = logging.getLogger('application')


class RFQFilters:

    def __init__(self, request):
        self.request = request
        self.db = MongoUtility()

    def _filter_by_company_type(self, query):
        company_key = 'company_id' if self.request.is_seeker else 'provider_ids'
        query[company_key] = self.request.company_id
        return query

    def _filter_by_rfq_status(self, query, rfq_status):
        query['rfq_status'] = rfq_status
        return query

    def _filter_by_search_term(self, query, search_by, search_term):
        if search_term and search_by:
            value = search_term.strip()

            if ',' in search_term:
                search_terms = [x.strip() for x in search_term.split(',') if x.strip()]

                if not search_terms:
                    return query

                value = {'$in': search_terms}

            query[search_by] = value

        return query

    def _get_data_filter(self):
        return {
            '_id': 0,
            'id': 1,
            'rfq_no': 1,
            'rfq_status': 1,
            'company_name': 1,
            'bound_type': 1,
            'service_mode': 1,
            'service_segment': 1,
            'service_type': 1,
            'contract_tenure': 1,
            'contract_start_date': 1,
            'contract_end_date': 1,
            'insured_by': 1,
            'fuel_type': 1,
            'cargo_types': 1,
            'product_category': 1,
            'product_desc': 1,
            'packing_type': 1,
            'handling_instructions': 1,
            'bidding_solution': 1,
            'bidding_start_time': 1,
            'bidding_end_time': 1,
            'pricing_basis': 1,
            'payment_terms': 1,
            'payment_cycle': 1,
            'advance_payment': 1,
            'chp_sent': 1,
            'chp_accepted': 1,
            'chp_rejected': 1,
            'cop_sent': 1,
            'cop_accepted': 1,
            'cop_rejected': 1,
            'loi_sent': 1,
            'loi_accepted': 1,
            'loi_rejected': 1,
            'loi_ignored': 1,
            'created_on': 1,
        }

    def get_total_counts(self, query):
        query['rfq_status'] = {'$in': [x.value for x in RFQStatus]}

        group_query = {
            "_id": "$rfq_status",
            "count": {"$sum": 1}
        }

        project_query = {
            "_id": 0,
            "rfq_status": "$_id",
            "count": 1
        }

        aggregate_result = list(self.db.aggregate(ProcurementDBColls.RFQ, query, group_query, project_query))
        return aggregate_result

    def run(self, get_query=False):
        params = self.request.validated_params

        query = {}
        query = self._filter_by_company_type(query)
        query = self._filter_by_rfq_status(query, params.rfq_status)
        query = self._filter_by_search_term(query, params.search_by, params.search_term)

        if get_query:
            return query

        df = self._get_data_filter()
        rfq_docs = self.db.find(ProcurementDBColls.RFQ, query, df, sort=[(params.sort_by, params.sort_order)]).limit(params.limit).skip(params.offset)
        return rfq_docs
