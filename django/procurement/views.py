import logging
from rest_framework import status
from utils.mongo import MongoUtility
from utils.constants import (
    AdminDBColls,
    SAASModules
)
from utils import (
    format_error_response,
    format_response,
)
from authn import (
    AuthenticateAll,
    Permissions,
    PermissionedAPIView
)
from schema import SettingsPayloadValidator

logger = logging.getLogger('application')


class CompanySpecificProductSettingsAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.Procurement.VIEW, ['GET'])
    ]

    def get(self, request, *args, **kwargs):
        params = request.GET
        try:
            product_id = int(params.get('product_id'))
        except (TypeError, ValueError):
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid product_id')

        seeker_id = params.get('seeker_id')

        db = MongoUtility(module_id=SAASModules.ADMIN.value)

        product_settings = {}
        if seeker_id:
            query = {
                'company_id': seeker_id,
                'product_id': product_id,
            }
            product_settings = db.find(AdminDBColls.FEATURE_SETTINGS, query, find_one=True)

        if not product_settings:
            product_settings = SettingsPayloadValidator().model_dump()

        response_data = product_settings
        return format_response(status.HTTP_200_OK, response_data, 'Success')
