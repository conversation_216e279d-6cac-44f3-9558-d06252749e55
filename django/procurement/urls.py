from django.urls import path
from .rfq_views import (
    CreateDraftRFQAPI,
    GetRFQDetailsAPI,
    UpdateDeleteRFQAPI,
    GetRFQAttachmentAPI,
    UploadDeleteRFQAttachmentAPI,
    DashboardCardsAPI,
    ListRFQsAPI,
    CountRFQsAPI,
    ListRFQProvidersAPI,
    CopyRFQAPI,
)
from .lane_views import (
    LaneCRUDAPI,
    StopPointCRUDAPI,
    ListLanesAPI,
    DownloadLanesTemplateAPI,
    UploadLanesAPI,
)
from .bidding_views import (
    AcceptTnCAPI,
    ExtendBiddingAPI,
    CloseBiddingAPI,
    UpdateBidAPI,
    SendChallengePriceAPI,
    ChallengePriceResponseAPI,
    SendCounterPriceAPI,
    CounterPriceResponseAPI,
    CreateUpdateLOIAPI,
    LOIResponseAPI,
    BidTrailAPI,
    DownloadRFQBidTrailAPI,
    StartOrEndBiddingAPI,
    UpdateLOIIgnoredAPI,
    ReprioritizeBiddersAPI,
    BulkSendLOIAPI
)
from .sse_views import TestEventsView, GetSSETokenAPI
from .views import CompanySpecificProductSettingsAPI

urlpatterns = [
    path('dashboard/cards/', DashboardCardsAPI.as_view(), name='dashboard_cards'),
    path('rfqs/listing', ListRFQsAPI.as_view(), name='list_rfqs'),
    path('rfqs/counts', CountRFQsAPI.as_view(), name='count_rfqs'),
    path('rfq/create-draft', CreateDraftRFQAPI.as_view(), name='create_draft_rfq'),
    path('rfq/<str:rfq_id>/', GetRFQDetailsAPI.as_view(), name='get_rfq_details'),
    path('rfq/<str:rfq_id>', UpdateDeleteRFQAPI.as_view(), name='update_delete_rfq'),
    path('rfq/<str:rfq_id>/copy/', CopyRFQAPI.as_view(), name='copy_rfq'),
    path('rfq/<str:rfq_id>/attachment/upload', UploadDeleteRFQAttachmentAPI.as_view(), name='upload_rfq_attachments'),
    path('rfq/<str:rfq_id>/attachment/<str:attachment_id>/', GetRFQAttachmentAPI.as_view(), name='get_rfq_attachment'),
    path('rfq/<str:rfq_id>/attachment/<str:attachment_id>', UploadDeleteRFQAttachmentAPI.as_view(), name='delete_rfq_attachment'),
    path('rfq/<str:rfq_id>/providers/listing', ListRFQProvidersAPI.as_view(), name='list_rfq_providers'),
    path('rfq/<str:rfq_id>/lane/create', LaneCRUDAPI.as_view(), name='create_lane'),
    path('rfq/<str:rfq_id>/lane/<str:lane_id>', LaneCRUDAPI.as_view(), name='get_update_delete_lane'),
    path('rfq/<str:rfq_id>/lane/<str:lane_id>/stop-point/create', StopPointCRUDAPI.as_view(), name='create_stop_point'),
    path('rfq/<str:rfq_id>/lane/<str:lane_id>/stop-point/<str:stop_point_id>', StopPointCRUDAPI.as_view(), name='update_delete_stop_point'),
    path('rfq/<str:rfq_id>/lanes/listing', ListLanesAPI.as_view(), name='list_lanes'),
    path('rfq/lanes/download-template/', DownloadLanesTemplateAPI.as_view(), name='download_lanes_template'),
    path('rfq/<str:rfq_id>/lanes/upload/', UploadLanesAPI.as_view(), name='upload_lanes'),
    path('rfq/<str:rfq_id>/accept-tnc', AcceptTnCAPI.as_view(), name='accept_tnc'),
    path('rfq/<str:rfq_id>/extend-bidding/', ExtendBiddingAPI.as_view(), name='extend_bidding'),
    path('rfq/<str:rfq_id>/close-bidding/', CloseBiddingAPI.as_view(), name='close_bidding'),
    path('rfq/<str:rfq_id>/lane/<str:lane_id>/provider/<str:provider_id>/update-bid', UpdateBidAPI.as_view(), name='update_bid'),
    path('rfq/<str:rfq_id>/lane/<str:lane_id>/provider/<str:provider_id>/send-challenge-price', SendChallengePriceAPI.as_view(), name='send_challenge_price'),
    path('rfq/<str:rfq_id>/lane/<str:lane_id>/provider/<str:provider_id>/challenge-price-response', ChallengePriceResponseAPI.as_view(), name='challenge_price_response'),
    path('rfq/<str:rfq_id>/lane/<str:lane_id>/provider/<str:provider_id>/send-counter-price', SendCounterPriceAPI.as_view(), name='send_counter_price'),
    path('rfq/<str:rfq_id>/lane/<str:lane_id>/provider/<str:provider_id>/counter-price-response', CounterPriceResponseAPI.as_view(), name='counter_price_response'),
    path('rfq/<str:rfq_id>/lane/<str:lane_id>/provider/<str:provider_id>/loi', CreateUpdateLOIAPI.as_view(), name='create_update_loi'),
    path('rfq/<str:rfq_id>/lane/<str:lane_id>/provider/<str:provider_id>/loi-response', LOIResponseAPI.as_view(), name='loi_response'),
    path('rfq/<str:rfq_id>/bulk-loi/', BulkSendLOIAPI.as_view(), name='bulk_send_loi'),
    path('rfq/<str:rfq_id>/lane/<str:lane_id>/provider/<str:provider_id>/bid-trail', BidTrailAPI.as_view(), name='bid_trail'),
    path('rfq/<str:rfq_id>/download-bid-trail', DownloadRFQBidTrailAPI.as_view(), name='download_rfq_bid_trail'),
    path('rfq/<str:rfq_id>/reprioritize-bidders', ReprioritizeBiddersAPI.as_view(), name='reprioritize_bidders'),
    path('start-or-end-bidding', StartOrEndBiddingAPI.as_view(), name='start_or_end_bidding'),
    path('update-loi-ignored', UpdateLOIIgnoredAPI.as_view(), name='update_loi_ignored'),
    path('sse/test/', TestEventsView.as_view(), name='test_sse'),
    path('sse/token/', GetSSETokenAPI.as_view(), name='get_sse_token'),
    path('settings/', CompanySpecificProductSettingsAPI.as_view(), name='settings'),
]
