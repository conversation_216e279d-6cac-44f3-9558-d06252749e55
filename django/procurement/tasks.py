import logging
from celery import shared_task
from django.conf import settings
from config.celery_conf import app  # noqa. Ensure the Celery app is imported
from config.celery_conf import load_dynamic_schedule

logger = logging.getLogger('celery')


# @app.task
@shared_task
def refresh_celery_schedule():
    # app.conf.beat_schedule = load_dynamic_schedule()
    settings.CELERY_BEAT_SCHEDULE = load_dynamic_schedule()
    return "Celery Beat Schedule Updated!"


@shared_task(bind=True)
def debug_task(self):
    message = 'Request: {0!r}'.format(self.request)
    logger.info(message)
    return message


@shared_task(bind=True)
def add(self, x, y):
    result = x + y
    logger.info(f'add {x} + {y} = {result}')
    return result


@shared_task(bind=True)
def bidding_start_end(self):
    from scripts.bidding_start_end import run
    return run()


@shared_task(bind=True)
def loi_ignored(self):
    from scripts.loi_ignored import run
    return run()


@shared_task(bind=True)
def auto_send_loi(self, rfq_ids):
    from scripts.auto_send_loi import run
    result = [run(rfq_id) for rfq_id in rfq_ids]
    return result
