import logging
from rest_framework import status
from django.utils.decorators import method_decorator
from utils.mongo import MongoUtility
from authn.decorators import validate_data_access, validate_request_payload
from utils.constants import (
    ProcurementDBColls,
    RFQStatus,
    BidStatus,
    ChallengeStatus,
    CounterStatus,
    LOIStatus,
    BiddingActions,
    RedisChannel,
)
from utils import (
    format_error_response,
    format_response,
)
from authn import (
    AuthenticateAll,
    AuthenticateSeeker,
    AuthenticateProvider,
    Permissions,
    PermissionedAPIView
)
from .request_validators import (
    UpdateBidPayloadValidator,
    SendOrCancelLOIPayloadValidator,
    BulkLOIPayloadValidator
)
from .app_utils import (
    log_bid_trail,
    log_bulk_bid_trail,
    update_auction_savings,
    update_lpp,
    download_bid_trail_excel,
    publish_event,
    get_old_bid,
    compute_savings_on_bid,
    compute_other_bidding_stats,
    update_bidding_ranks,
    get_product_settings,
    get_lanes_without_minimum_bidders_for_loi
)
from notifications.manager import (
    send_loi_notification,
    send_accept_loi_notification,
    send_reject_loi_notification,
    send_withdraw_bid_notification,
    send_challenge_price_notification
)

logger = logging.getLogger('application')


class StartOrEndBiddingAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.Procurement.VIEW, ['POST'])
    ]

    def post(self, request, *args, **kwargs):
        from scripts.bidding_start_end import run
        message = run()
        return format_response(status.HTTP_200_OK, {'message': message}, 'Success')


class UpdateLOIIgnoredAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.Procurement.VIEW, ['POST'])
    ]

    def post(self, request, *args, **kwargs):
        from scripts.loi_ignored import run
        message = run()
        return format_response(status.HTTP_200_OK, {'message': message}, 'Success')


class ReprioritizeBiddersAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.Procurement.VIEW, ['POST'])
    ]

    @validate_data_access(['rfq'])
    def post(self, request, *args, **kwargs):
        rfq_doc = request.rfq_doc

        update_bidding_ranks(rfq_doc, {}, update_all_ranks=True)

        return format_response(status.HTTP_200_OK, {}, 'Success')


class AcceptTnCAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateProvider, )
    permissions = [
        (Permissions.Procurement.UPDATE_BID, ['POST'])
    ]

    @validate_data_access(['lane_provider', 'rfq'])
    def post(self, request, *args, **kwargs):
        rfq_id = kwargs['rfq_id']
        rfq_doc = request.rfq_doc

        db = MongoUtility()

        query = {'rfq_id': rfq_id, 'provider_id': request.company_id}

        if rfq_doc['rfq_status'] == RFQStatus.COMPLETED.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Bidding time has ended.')
        elif rfq_doc['rfq_status'] != RFQStatus.BIDDING_IN_PROGRESS.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Bidding has not started yet.')

        update_query = {'is_tnc_accepted': True, 'updated_on': request.now}
        db.update(ProcurementDBColls.LANE_PROVIDERS, query, update_query, update_many=True)

        response_data = rfq_doc
        response_data.update(update_query)
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class CloseBiddingAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.UPDATE_RFQ, ['POST'])
    ]

    @validate_data_access(['rfq'])
    def post(self, request, *args, **kwargs):
        rfq_doc = request.rfq_doc

        if rfq_doc['rfq_status'] == RFQStatus.COMPLETED.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Bidding has already ended.')
        elif rfq_doc['rfq_status'] != RFQStatus.BIDDING_IN_PROGRESS.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Bidding has not yet started.')

        db = MongoUtility()

        query = {'id': rfq_doc['id'], 'company_id': request.company_id}
        update_query = {
            'bidding_end_time': request.now,
            'rfq_status': RFQStatus.COMPLETED.value,
            'updated_on': request.now
        }
        updated_rfq_doc = db.update(ProcurementDBColls.RFQ, query, update_query, find_one_and_update=True)

        company_ids = [request.company_id] + rfq_doc['provider_ids']
        publish_event(RedisChannel.BIDDING_STATUS.value, company_ids=company_ids)
        publish_event(RedisChannel.RFQ_STATUS_UPDATE.value, rfq_ids=[rfq_doc['id']])

        product_settings = get_product_settings(rfq_doc['company_id'])
        auto_send_loi_to_l1 = product_settings.get('auto_send_loi_to_l1')
        if auto_send_loi_to_l1:
            from .tasks import auto_send_loi
            auto_send_loi.delay(rfq_ids=[rfq_doc['id']])

        response_data = updated_rfq_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class ExtendBiddingAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.UPDATE_RFQ, ['POST'])
    ]

    @validate_data_access(['rfq'])
    def post(self, request, *args, **kwargs):
        rfq_doc = request.rfq_doc

        try:
            bidding_end_time = int(request.data['bidding_end_time'])
            if (bidding_end_time <= request.now) or (bidding_end_time <= rfq_doc['bidding_end_time']):
                raise ValueError
        except (KeyError, TypeError, ValueError):
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Bid end time must be greater than current time and previous bid end time.')

        db = MongoUtility()

        query = {
            'rfq_id': kwargs['rfq_id'],
            '$or': [
                {'challenge_status_id': {'$ne': ChallengeStatus.NOT_SENT.value}},
                {'loi_status_id': {'$ne': LOIStatus.NOT_SENT.value}},
            ]
        }
        can_extend_bidding = db.find(ProcurementDBColls.LANE_PROVIDERS, query).count() <= 0
        if not can_extend_bidding:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Already sent challenge price or LOI to some providers. Bidding cannot be extended.')

        query = {'id': kwargs['rfq_id'], 'company_id': request.company_id}
        update_query = {
            'rfq_status': RFQStatus.BIDDING_IN_PROGRESS.value,
            'bidding_end_time': bidding_end_time,
            'updated_on': request.now
        }

        updated_rfq_doc = db.update(ProcurementDBColls.RFQ, query, update_query, find_one_and_update=True)

        company_ids = [request.company_id] + rfq_doc['provider_ids']
        publish_event(RedisChannel.BIDDING_STATUS.value, company_ids=company_ids)
        publish_event(RedisChannel.RFQ_STATUS_UPDATE.value, rfq_ids=[rfq_doc['id']])

        response_data = updated_rfq_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')


@method_decorator(validate_request_payload(UpdateBidPayloadValidator), name='post')
class UpdateBidAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateProvider, )
    permissions = [
        (Permissions.Procurement.UPDATE_BID, ['POST'])
    ]

    @validate_data_access(['rfq', 'lane_provider'])
    def post(self, request, *args, **kwargs):
        # grace_period = 3000  # 3sec grace to account for network latencies and request pre-processing times
        current_time_with_grace = request.now  # - grace_period
        rfq_doc = request.rfq_doc
        lp_doc = request.lp_doc
        payload = request.validated_payload
        previous_bid = lp_doc['bid_price']
        new_bid = payload.bid_price
        rfq_id = rfq_doc['id']
        lane_id = lp_doc['lane_id']
        provider_id = request.company_id
        product_settings = get_product_settings(rfq_doc['company_id'])
        bid_decremental = product_settings.get('bid_decremental') or 0
        bid_tolerance = product_settings.get('bid_tolerance') or 0
        l1_rate_decremental = product_settings.get('l1_rate_decremental') or 0

        db = MongoUtility()

        lane_doc = db.find(ProcurementDBColls.LANES, {'id': lane_id}, find_one=True)

        l1_rate = lane_doc.get('l1_rate')
        ceiling_price = lane_doc['rate']

        try:
            if (rfq_doc['rfq_status'] == RFQStatus.COMPLETED.value) or (rfq_doc['bidding_end_time'] < current_time_with_grace):
                raise ValueError('Bidding time has ended.')
            elif rfq_doc['rfq_status'] != RFQStatus.BIDDING_IN_PROGRESS.value:
                raise ValueError('Bidding has not started yet.')
            elif not lp_doc['is_tnc_accepted']:
                raise ValueError('Please read and accept the Terms & Conditions before placing a Bid.')
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

        if payload.bid_status_id == BidStatus.BIDDING.value:
            if new_bid > ceiling_price:
                return format_error_response(status.HTTP_400_BAD_REQUEST, 'Bid cannot be higher than ceiling price.')

            if previous_bid:
                new_bid_decrement = previous_bid - new_bid
                if new_bid >= previous_bid:
                    return format_error_response(status.HTTP_400_BAD_REQUEST, 'Bid must be lower than previous bid.')
            else:
                new_bid_decrement = ceiling_price - new_bid
                if bid_tolerance:
                    tolerance_amt = round((bid_tolerance / 100) * ceiling_price)
                    if new_bid > (ceiling_price - tolerance_amt):
                        return format_error_response(status.HTTP_400_BAD_REQUEST, f'Bid must be at least ₹{tolerance_amt}/- lower than ceiling price.')

            if new_bid_decrement < bid_decremental:
                return format_error_response(status.HTTP_400_BAD_REQUEST, f'Bid must be at least ₹{bid_decremental}/- lower than previous bid.')

            if (l1_rate and l1_rate_decremental):
                min_valid_amount = l1_rate - l1_rate_decremental
                if (min_valid_amount > 0) and (new_bid > min_valid_amount):
                    if not previous_bid:
                        return format_error_response(status.HTTP_400_BAD_REQUEST, f'Bid must be at least ₹{min_valid_amount}/- or lower.')
                    else:
                        decrement_amount = previous_bid - min_valid_amount
                        return format_error_response(status.HTTP_400_BAD_REQUEST, f'Bid must be at least ₹{decrement_amount}/- lower than previous bid.')

            bidding_action, price = BiddingActions.BID_PLACED, new_bid
            bid_status = BidStatus(payload.bid_status_id)
            savings_amt, savings_percent = compute_savings_on_bid(ceiling_price, new_bid)

        elif payload.bid_status_id == BidStatus.WITHDRAWN.value:
            bidding_action, price = BiddingActions.BID_WITHDRAWN, previous_bid
            if not previous_bid:
                return format_error_response(status.HTTP_400_BAD_REQUEST, 'Cannot withdraw bids any further.')

            new_bid = get_old_bid(lane_id, provider_id, previous_bid)
            bid_status = BidStatus.NO_BID if not new_bid else BidStatus.BIDDING
            savings_amt, savings_percent = compute_savings_on_bid(ceiling_price, new_bid)

        update_query = {
            'bid_price': new_bid,
            'loi_price': new_bid,
            'bid_status_id': bid_status.value,
            'bid_status': bid_status.name,
            'ip': request.ip,
            'savings_amt': savings_amt,
            'savings_percent': savings_percent,
            'updated_on': request.now
        }

        lp_query = {'lane_id': lane_id, 'provider_id': provider_id}
        updated_lp_doc = db.update(ProcurementDBColls.LANE_PROVIDERS, lp_query, update_query, find_one_and_update=True)

        log_bid_trail(lp_doc, bidding_action, price, request.ip, request.now)

        l1 = update_bidding_ranks(rfq_doc, lp_doc)

        lane_update_query, lane_inc_query = compute_other_bidding_stats(rfq_doc, lane_doc, lp_doc, l1, new_bid)
        if lane_doc.get('l1_rate') != l1.get('bid_price'):
            lane_update_query['l1_rate'] = l1['bid_price']

        if lane_update_query or lane_inc_query:
            lane_update_query['updated_on'] = request.now
            db.update(ProcurementDBColls.LANES, {'id': lane_id}, lane_update_query, inc_query=lane_inc_query)

        publish_event(RedisChannel.AUCTION_UPDATES.value, rfq_ids=[rfq_id])

        # Send withdraw bid notification if bid was withdrawn
        if payload.bid_status_id == BidStatus.WITHDRAWN.value:
            try:
                notification_result = send_withdraw_bid_notification(
                    rfq_id=rfq_id,
                    lane_id=lane_id,
                    provider_id=provider_id,
                    rfq_no=rfq_doc.get('rfq_no'),
                    company_id=rfq_doc['company_id'],
                    company_name=rfq_doc['company_name'],
                    withdrawn_price=previous_bid,
                    withdrawal_reason=getattr(payload, 'withdrawal_reason', 'Not specified'),
                    send_email=True,
                    send_sms=False
                )

                if notification_result['email_sent']:
                    logger.info(f"Withdraw bid notification sent successfully for RFQ {rfq_id}, Lane {lane_id}")
                else:
                    logger.warning(f"Failed to send withdraw bid notification for RFQ {rfq_id}, Lane {lane_id}")

            except Exception as e:
                logger.error(f"Error sending withdraw bid notification for RFQ {rfq_id}, Lane {lane_id}: {str(e)}")

        response_data = updated_lp_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class SendChallengePriceAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.CREATE_CHALLENGE_PRICE, ['POST'])
    ]

    @validate_data_access(['rfq', 'lane', 'lane_provider'])
    def post(self, request, *args, **kwargs):
        rfq_doc = request.rfq_doc
        lp_doc = request.lp_doc
        product_settings = get_product_settings(rfq_doc['company_id'])
        enable_chp = product_settings.get('enable_chp_with_cop')

        try:
            if not enable_chp:
                raise ValueError('Challenge price is disabled.')
            elif rfq_doc['rfq_status'] != RFQStatus.COMPLETED.value:
                raise ValueError('Bidding has not ended yet.')
            elif lp_doc['loi_status_id'] != LOIStatus.NOT_SENT.value:
                raise ValueError('LOI already sent.')
            elif not lp_doc['bid_price']:
                raise ValueError('Vendor did not participate in bidding.')
            elif lp_doc['challenge_status_id'] == ChallengeStatus.SENT.value:
                raise ValueError('Challenge price already sent.')
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

        try:
            challenge_price = int(request.data.get('challenge_price') or 0)
            if challenge_price >= lp_doc['bid_price']:
                return format_error_response(status.HTTP_400_BAD_REQUEST, 'Challenge price must be less than bid price.')
        except (TypeError, ValueError):
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid challenge price.')

        db = MongoUtility()
        query = {
            'rfq_id': kwargs['rfq_id'],
            'lane_id': kwargs['lane_id'],
            'provider_id': kwargs['provider_id'],
        }
        update_query = {
            'challenge_price': challenge_price,
            'challenge_status_id': ChallengeStatus.SENT.value,
            'challenge_status': ChallengeStatus.SENT.name,
            'updated_on': request.now
        }
        updated_lp_doc = db.update(ProcurementDBColls.LANE_PROVIDERS, query, update_query, find_one_and_update=True)
        log_bid_trail(lp_doc, BiddingActions.CHALLENGE_PRICE_SENT, challenge_price, request.ip, request.now)

        db.update(ProcurementDBColls.RFQ, {'id': rfq_doc['id']}, inc_query={'chp_sent': 1})

        publish_event(RedisChannel.AUCTION_UPDATES.value, rfq_ids=[rfq_doc['id']])
        publish_event(RedisChannel.BIDDING_STATUS.value, company_ids=[rfq_doc['company_id'], lp_doc['provider_id']])

        # Send challenge price notification
        try:
            notification_result = send_challenge_price_notification(
                rfq_id=kwargs['rfq_id'],
                lane_id=kwargs['lane_id'],
                provider_id=kwargs['provider_id'],
                rfq_no=rfq_doc.get('rfq_no'),
                company_id=rfq_doc['company_id'],
                company_name=rfq_doc['company_name'],
                challenge_price=challenge_price,
                current_bid=lp_doc['bid_price'],
                send_email=True,
                send_sms=False
            )

            if notification_result['email_sent']:
                logger.info(f"Challenge price notification sent successfully for RFQ {kwargs['rfq_id']}, Lane {kwargs['lane_id']}")
            else:
                logger.warning(f"Failed to send challenge price notification for RFQ {kwargs['rfq_id']}, Lane {kwargs['lane_id']}")

        except Exception as e:
            logger.error(f"Error sending challenge price notification for RFQ {kwargs['rfq_id']}, Lane {kwargs['lane_id']}: {str(e)}")

        response_data = updated_lp_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class ChallengePriceResponseAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateProvider, )
    permissions = [
        (Permissions.Procurement.RESPOND_CHALLENGE_PRICE, ['POST'])
    ]

    @validate_data_access(['rfq', 'lane_provider'])
    def post(self, request, *args, **kwargs):
        rfq_doc = request.rfq_doc
        lp_doc = request.lp_doc

        try:
            if lp_doc['challenge_status_id'] == ChallengeStatus.NOT_SENT.value:
                raise ValueError('Challenge Price not recieved.')
            elif lp_doc['challenge_status_id'] in [ChallengeStatus.ACCEPTED.value, ChallengeStatus.REJECTED.value]:
                raise ValueError(f"Challenge Price already {lp_doc['challenge_status']}.")
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

        is_chp_accepted = request.data.get('response') is True
        if is_chp_accepted:
            loi_price = lp_doc['challenge_price']
            challenge_status = ChallengeStatus.ACCEPTED
            bidding_action = BiddingActions.CHALLENGE_PRICE_ACCEPTED
        else:
            loi_price = lp_doc['loi_price']
            challenge_status = ChallengeStatus.REJECTED
            bidding_action = BiddingActions.CHALLENGE_PRICE_REJECTED

        db = MongoUtility()
        query = {
            'rfq_id': kwargs['rfq_id'],
            'lane_id': kwargs['lane_id'],
            'provider_id': kwargs['provider_id'],
        }
        update_query = {
            'loi_price': loi_price,
            'challenge_status_id': challenge_status.value,
            'challenge_status': challenge_status.name,
            'updated_on': request.now
        }
        updated_lp_doc = db.update(ProcurementDBColls.LANE_PROVIDERS, query, update_query, find_one_and_update=True)
        log_bid_trail(lp_doc, bidding_action, lp_doc['challenge_price'], request.ip, request.now)

        if is_chp_accepted:
            l1 = update_bidding_ranks(rfq_doc, lp_doc)
            inc_query = {'chp_accepted': 1}

            lane_doc = db.find(ProcurementDBColls.LANES, {'id': lp_doc['lane_id']}, {'_id': 0, 'l1_rate': 1}, find_one=True)
            if lane_doc.get('l1_rate') != l1['loi_price']:
                lane_update_query = {'l1_rate': l1['loi_price'], 'updated_on': request.now}
                db.update(ProcurementDBColls.LANES, {'id': lp_doc['lane_id']}, lane_update_query)
        else:
            inc_query = {'chp_rejected': 1}

        db.update(ProcurementDBColls.RFQ, {'id': rfq_doc['id']}, inc_query=inc_query)

        publish_event(RedisChannel.AUCTION_UPDATES.value, rfq_ids=[rfq_doc['id']])
        publish_event(RedisChannel.BIDDING_STATUS.value, company_ids=[rfq_doc['company_id'], lp_doc['provider_id']])

        response_data = updated_lp_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class SendCounterPriceAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateProvider, )
    permissions = [
        (Permissions.Procurement.CREATE_COUNTER_PRICE, ['POST'])
    ]

    @validate_data_access(['rfq', 'lane_provider'])
    def post(self, request, *args, **kwargs):
        rfq_doc = request.rfq_doc
        lp_doc = request.lp_doc

        try:
            if rfq_doc['rfq_status'] != RFQStatus.COMPLETED.value:
                raise ValueError('Bidding has not ended yet.')
            elif lp_doc['challenge_status_id'] == ChallengeStatus.NOT_SENT.value:
                raise ValueError('Challenge price not received.')
            elif lp_doc['challenge_status_id'] == ChallengeStatus.SENT.value:
                raise ValueError('Challenge price must be rejected before sending counter price.')
            elif lp_doc['challenge_status_id'] == ChallengeStatus.ACCEPTED.value:
                raise ValueError('Challenge price already accepted.')
            elif lp_doc['loi_status_id'] != LOIStatus.NOT_SENT.value:
                raise ValueError('LOI already received.')
            elif not lp_doc['bid_price']:
                raise ValueError('Vendor did not participate in bidding.')
            elif lp_doc['counter_status_id'] == CounterStatus.SENT.value:
                raise ValueError('Counter price already sent.')
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

        try:
            counter_price = int(request.data.get('counter_price') or 0)
            if counter_price > lp_doc['bid_price']:
                return format_error_response(status.HTTP_400_BAD_REQUEST, 'Counter price cannot be greater than bid price.')
        except (TypeError, ValueError):
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid counter price.')

        db = MongoUtility()
        query = {
            'rfq_id': kwargs['rfq_id'],
            'lane_id': kwargs['lane_id'],
            'provider_id': kwargs['provider_id'],
        }
        update_query = {
            'counter_price': counter_price,
            'counter_status_id': CounterStatus.SENT.value,
            'counter_status': CounterStatus.SENT.name,
            'updated_on': request.now
        }
        updated_lp_doc = db.update(ProcurementDBColls.LANE_PROVIDERS, query, update_query, find_one_and_update=True)
        log_bid_trail(lp_doc, BiddingActions.COUNTER_PRICE_SENT, counter_price, request.ip, request.now)

        db.update(ProcurementDBColls.RFQ, {'id': rfq_doc['id']}, inc_query={'cop_sent': 1})

        publish_event(RedisChannel.AUCTION_UPDATES.value, rfq_ids=[rfq_doc['id']])
        publish_event(RedisChannel.BIDDING_STATUS.value, company_ids=[rfq_doc['company_id'], lp_doc['provider_id']])

        response_data = updated_lp_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class CounterPriceResponseAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.RESPOND_COUNTER_PRICE, ['POST'])
    ]

    @validate_data_access(['rfq', 'lane', 'lane_provider'])
    def post(self, request, *args, **kwargs):
        rfq_doc = request.rfq_doc
        lp_doc = request.lp_doc

        try:
            if lp_doc['counter_status_id'] == CounterStatus.NOT_SENT.value:
                raise ValueError('Counter Price not recieved.')
            elif lp_doc['counter_status_id'] in [CounterStatus.ACCEPTED.value, CounterStatus.REJECTED.value]:
                raise ValueError(f"Counter Price already {lp_doc['counter_status']}.")
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

        is_cop_accepted = request.data.get('response') is True
        if is_cop_accepted:
            loi_price = lp_doc['counter_price']
            counter_status = CounterStatus.ACCEPTED
            bidding_action = BiddingActions.COUNTER_PRICE_ACCEPTED
        else:
            loi_price = lp_doc['loi_price']
            counter_status = CounterStatus.REJECTED
            bidding_action = BiddingActions.COUNTER_PRICE_REJECTED

        db = MongoUtility()
        query = {
            'rfq_id': kwargs['rfq_id'],
            'lane_id': kwargs['lane_id'],
            'provider_id': kwargs['provider_id'],
        }
        update_query = {
            'loi_price': loi_price,
            'counter_status_id': counter_status.value,
            'counter_status': counter_status.name,
            'updated_on': request.now
        }
        updated_lp_doc = db.update(ProcurementDBColls.LANE_PROVIDERS, query, update_query, find_one_and_update=True)
        log_bid_trail(lp_doc, bidding_action, lp_doc['counter_price'], request.ip, request.now)

        if is_cop_accepted:
            l1 = update_bidding_ranks(rfq_doc, lp_doc)
            inc_query = {'cop_accepted': 1}

            lane_doc = db.find(ProcurementDBColls.LANES, {'id': lp_doc['lane_id']}, {'_id': 0, 'l1_rate': 1}, find_one=True)
            if lane_doc.get('l1_rate') != l1['loi_price']:
                lane_update_query = {'l1_rate': l1['loi_price'], 'updated_on': request.now}
                db.update(ProcurementDBColls.LANES, {'id': lp_doc['lane_id']}, lane_update_query)
        else:
            inc_query = {'cop_rejected': 1}

        db.update(ProcurementDBColls.RFQ, {'id': rfq_doc['id']}, inc_query=inc_query)

        publish_event(RedisChannel.AUCTION_UPDATES.value, rfq_ids=[rfq_doc['id']])
        publish_event(RedisChannel.BIDDING_STATUS.value, company_ids=[rfq_doc['company_id'], lp_doc['provider_id']])

        response_data = updated_lp_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')


@method_decorator(validate_request_payload(SendOrCancelLOIPayloadValidator), name='post')
@method_decorator(validate_request_payload(SendOrCancelLOIPayloadValidator), name='put')
class CreateUpdateLOIAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.CREATE_LOI, ['POST']),
        (Permissions.Procurement.UPDATE_LOI, ['PUT'])
    ]

    @validate_data_access(['rfq', 'lane', 'lane_provider'])
    def post(self, request, *args, **kwargs):
        rfq_doc = request.rfq_doc
        lp_doc = request.lp_doc
        payload = request.validated_payload
        product_settings = get_product_settings(rfq_doc['company_id'])
        min_bidders_for_loi = product_settings.get('min_bidders_for_loi')

        try:
            if rfq_doc['rfq_status'] != RFQStatus.COMPLETED.value:
                raise ValueError('Bidding has not ended yet.')
            elif lp_doc['loi_status_id'] in [LOIStatus.SENT.value, LOIStatus.CANCELLED.value]:
                raise ValueError(f"LOI already {lp_doc['loi_status']}.")
            elif lp_doc['bid_status_id'] == BidStatus.NO_BID.value:
                raise ValueError(f"Transporter did not place any bids.")
            elif payload.loi_status_id != LOIStatus.SENT.value:
                raise ValueError('Invalid LOI status.')
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

        db = MongoUtility()

        query = {
            'rfq_id': kwargs['rfq_id'],
            'lane_id': kwargs['lane_id'],
            'priority': {'$ne': None},
        }

        total_bidders = len(db.distinct(ProcurementDBColls.LANE_PROVIDERS, 'priority', query))
        if total_bidders < min_bidders_for_loi:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Minimum {min_bidders_for_loi} bidders required for sending LOI.')

        del query['priority']
        query['provider_id'] = kwargs['provider_id']

        update_query = {
            'loi_status_id': payload.loi_status_id,
            'loi_status': LOIStatus(payload.loi_status_id).name,
            'loi_start_time': payload.loi_start_time,
            'loi_end_time': payload.loi_end_time,
            'loi_instructions': payload.loi_instructions,
            'loi_remarks': payload.loi_remarks,
            'updated_on': request.now
        }
        updated_lp_doc = db.update(ProcurementDBColls.LANE_PROVIDERS, query, update_query, find_one_and_update=True)
        log_bid_trail(lp_doc, BiddingActions.LOI_SENT, lp_doc['loi_price'], request.ip, request.now)

        db.update(ProcurementDBColls.RFQ, {'id': rfq_doc['id']}, inc_query={'loi_sent': 1})

        publish_event(RedisChannel.AUCTION_UPDATES.value, rfq_ids=[rfq_doc['id']])
        publish_event(RedisChannel.BIDDING_STATUS.value, company_ids=[rfq_doc['company_id'], lp_doc['provider_id']])

        # Send LOI notification
        try:
            notification_result = send_loi_notification(
                rfq_id=kwargs['rfq_id'],
                lane_id=kwargs['lane_id'],
                provider_id=kwargs['provider_id'],
                rfq_no=rfq_doc.get('rfq_no'),
                company_id=rfq_doc['company_id'],
                company_name=rfq_doc['company_name'],
                loi_price=lp_doc['loi_price'],
                send_email=True,
                send_sms=False
            )

            if notification_result['email_sent']:
                logger.info(f"LOI notification sent successfully for RFQ {kwargs['rfq_id']}, Lane {kwargs['lane_id']}")
            else:
                logger.warning(f"Failed to send LOI notification for RFQ {kwargs['rfq_id']}, Lane {kwargs['lane_id']}")

        except Exception as e:
            logger.error(f"Error sending LOI notification for RFQ {kwargs['rfq_id']}, Lane {kwargs['lane_id']}: {str(e)}")

        response_data = updated_lp_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    @validate_data_access(['rfq', 'lane', 'lane_provider'])
    def put(self, request, *args, **kwargs):
        rfq_doc = request.rfq_doc
        lp_doc = request.lp_doc
        payload = request.validated_payload

        try:
            if rfq_doc['rfq_status'] != RFQStatus.COMPLETED.value:
                raise ValueError('Bidding has not ended yet.')
            elif lp_doc['loi_status_id'] == LOIStatus.NOT_SENT.value:
                raise ValueError(f"LOI not sent yet.")
            elif lp_doc['loi_status_id'] != LOIStatus.SENT.value:
                raise ValueError(f"LOI already {lp_doc['loi_status']}.")
            elif lp_doc['loi_end_time'] < request.now:
                raise ValueError("LOI has expired.")
            elif payload.loi_status_id != LOIStatus.CANCELLED.value:
                raise ValueError(f"Invalid LOI status.")
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

        db = MongoUtility()
        query = {
            'rfq_id': kwargs['rfq_id'],
            'lane_id': kwargs['lane_id'],
            'provider_id': kwargs['provider_id'],
        }
        update_query = {
            'loi_status_id': payload.loi_status_id,
            'loi_status': LOIStatus(payload.loi_status_id).name,
            'loi_cancel_reason': payload.loi_cancel_reason,
            'updated_on': request.now
        }
        updated_lp_doc = db.update(ProcurementDBColls.LANE_PROVIDERS, query, update_query, find_one_and_update=True)
        log_bid_trail(lp_doc, BiddingActions.LOI_CANCELLED, lp_doc['loi_price'], request.ip, request.now)

        db.update(ProcurementDBColls.RFQ, {'id': rfq_doc['id']}, inc_query={'loi_sent': -1})

        publish_event(RedisChannel.AUCTION_UPDATES.value, rfq_ids=[rfq_doc['id']])
        publish_event(RedisChannel.BIDDING_STATUS.value, company_ids=[rfq_doc['company_id'], lp_doc['provider_id']])

        response_data = updated_lp_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class LOIResponseAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateProvider, )
    permissions = [
        (Permissions.Procurement.RESPOND_LOI, ['POST'])
    ]

    @ validate_data_access(['rfq', 'lane_provider'])
    def post(self, request, *args, **kwargs):
        rfq_doc = request.rfq_doc
        lp_doc = request.lp_doc

        db = MongoUtility()

        lane_query = {'id': kwargs['lane_id'], 'rfq_id': rfq_doc['id']}
        lane_doc = db.find(ProcurementDBColls.LANES, lane_query, find_one=True)

        try:
            if lp_doc['loi_status_id'] == LOIStatus.NOT_SENT.value:
                raise ValueError('LOI not recieved.')
            elif lp_doc['loi_start_time'] > request.now:
                raise ValueError("LOI isn't active yet.")
            elif lp_doc['loi_end_time'] < request.now:
                raise ValueError("LOI has expired.")
            elif lp_doc['loi_status_id'] == LOIStatus.CANCELLED.value:
                raise ValueError('LOI has been cancelled.')
            elif lp_doc['loi_status_id'] in [LOIStatus.ACCEPTED.value, LOIStatus.REJECTED.value]:
                raise ValueError(f"LOI already {lp_doc['loi_status']}.")
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

        user_response = request.data.get('response')
        if user_response is True:
            loi_status = LOIStatus.ACCEPTED
            bidding_action = BiddingActions.LOI_ACCEPTED
            inc_query = {'loi_accepted': 1}
            update_auction_savings(rfq_doc['company_id'], lp_doc['loi_price'], lane_doc['rate'])
            update_lpp(rfq_doc, lane_doc, lp_doc)
        else:
            loi_status = LOIStatus.REJECTED
            bidding_action = BiddingActions.LOI_REJECTED
            inc_query = {'loi_rejected': 1}

        query = {
            'rfq_id': kwargs['rfq_id'],
            'lane_id': kwargs['lane_id'],
            'provider_id': kwargs['provider_id'],
        }
        update_query = {
            'loi_status_id': loi_status.value,
            'loi_status': loi_status.name,
            'updated_on': request.now
        }
        updated_lp_doc = db.update(ProcurementDBColls.LANE_PROVIDERS, query, update_query, find_one_and_update=True)
        log_bid_trail(lp_doc, bidding_action, lp_doc['loi_price'], request.ip, request.now)

        db.update(ProcurementDBColls.RFQ, {'id': rfq_doc['id']}, inc_query=inc_query)

        publish_event(RedisChannel.AUCTION_UPDATES.value, rfq_ids=[rfq_doc['id']])
        publish_event(RedisChannel.BIDDING_STATUS.value, company_ids=[rfq_doc['company_id'], lp_doc['provider_id']])

        # Send LOI response notification
        try:
            if user_response is True:
                # Send Accept LOI notification
                notification_result = send_accept_loi_notification(
                    rfq_id=kwargs['rfq_id'],
                    lane_id=kwargs['lane_id'],
                    provider_id=kwargs['provider_id'],
                    rfq_no=rfq_doc.get('rfq_no'),
                    company_id=rfq_doc['company_id'],
                    company_name=rfq_doc['company_name'],
                    loi_price=lp_doc['loi_price'],
                    send_email=True,
                    send_sms=False
                )
                action = "accepted"
            else:
                # Send Reject LOI notification
                notification_result = send_reject_loi_notification(
                    rfq_id=kwargs['rfq_id'],
                    lane_id=kwargs['lane_id'],
                    provider_id=kwargs['provider_id'],
                    rfq_no=rfq_doc.get('rfq_no'),
                    company_id=rfq_doc['company_id'],
                    company_name=rfq_doc['company_name'],
                    loi_price=lp_doc['loi_price'],
                    rejection_reason=request.data.get('reason', 'Not specified'),
                    send_email=True,
                    send_sms=False
                )
                action = "rejected"

            if notification_result['email_sent']:
                logger.info(f"LOI {action} notification sent successfully for RFQ {kwargs['rfq_id']}, Lane {kwargs['lane_id']}")
            else:
                logger.warning(f"Failed to send LOI {action} notification for RFQ {kwargs['rfq_id']}, Lane {kwargs['lane_id']}")

        except Exception as e:
            logger.error(f"Error sending LOI response notification for RFQ {kwargs['rfq_id']}, Lane {kwargs['lane_id']}: {str(e)}")

        response_data = updated_lp_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class BidTrailAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.Procurement.VIEW, ['GET'])
    ]

    @ validate_data_access(['rfq', 'lane_provider'])
    def get(self, request, *args, **kwargs):
        db = MongoUtility()
        query = {
            'rfq_id': kwargs['rfq_id'],
            'id': kwargs['lane_id'],
            'company_id': request.rfq_doc['company_id'],
        }
        lane_doc = db.find(ProcurementDBColls.LANES, query, find_one=True)

        query['lane_id'] = query.pop('id')
        query['provider_id'] = kwargs['provider_id']
        bid_trail_docs = [x for x in db.find(ProcurementDBColls.BID_TRAIL, query, sort=[('_id', -1)])]

        if request.GET.get('action') == 'download':
            try:
                return download_bid_trail_excel(request.rfq_doc, [lane_doc], bid_trail_docs)
            except ValueError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

        response_data = {
            'bid_trail': bid_trail_docs
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class DownloadRFQBidTrailAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.Procurement.VIEW, ['GET'])
    ]

    @ validate_data_access(['rfq'])
    def get(self, request, *args, **kwargs):
        db = MongoUtility()
        query = {
            'rfq_id': kwargs['rfq_id'],
            'company_id': request.rfq_doc['company_id']
        }
        if request.is_provider:
            query['provider_id'] = request.company_id

        bid_trail_docs = [x for x in db.find(ProcurementDBColls.BID_TRAIL, query, sort=[('lane_id', 1), ('provider_id', 1), ('_id', -1)])]

        query.pop('provider_id', None)
        lane_docs = [x for x in db.find(ProcurementDBColls.LANES, query, sort=[('_id', -1)])]

        try:
            return download_bid_trail_excel(request.rfq_doc, lane_docs, bid_trail_docs)
        except ValueError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))


@method_decorator(validate_request_payload(BulkLOIPayloadValidator), name='post')
class BulkSendLOIAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.Procurement.CREATE_LOI, ['POST'])
    ]

    @validate_data_access(['rfq'])
    def post(self, request, *args, **kwargs):
        rfq_doc = request.rfq_doc
        rfq_id = rfq_doc['id']
        payload = request.validated_payload
        product_settings = get_product_settings(rfq_doc['company_id'])
        min_bidders_for_loi = product_settings.get('min_bidders_for_loi')

        # Check if bidding has ended
        if rfq_doc['rfq_status'] != RFQStatus.COMPLETED.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Bidding has not ended yet.')

        if payload.loi_status_id == LOIStatus.CANCELLED.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Cannot cancel LOI in bulk.')

        db = MongoUtility()

        invalid_lanes = get_lanes_without_minimum_bidders_for_loi(rfq_id, min_bidders_for_loi)
        if invalid_lanes:
            lane_nos = ','.join([x['lane_no'] for x in invalid_lanes])
            return format_error_response(status.HTTP_400_BAD_REQUEST, f"Lanes-{lane_nos} : Minimum {min_bidders_for_loi} bidders required for sending LOI.")

        # Determine which lane-providers to send LOI to based on selection method
        query = {
            'rfq_id': rfq_id,
            'loi_status_id': LOIStatus.NOT_SENT.value,
            'priority': {'$ne': None}
        }
        if payload.lane_provider_ids:
            # Use the specified lane-provider ids directly
            query['id'] = {'$in': payload.lane_provider_ids}
        elif payload.priorities:
            # Get lane-providers with specified priorities
            query['priority'] = {'$in': payload.priorities}
        elif payload.lane_ids:
            # Get lane-providers with specified lanes
            query['lane_id'] = {'$in': payload.lane_ids}
        elif payload.all_lanes:
            # Get all lane-providers where LOI is not sent and rank was assigned
            pass
        else:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Missing selection parameter.')

        df = {
            '_id': 0,
            'id': 1,
            'rfq_id': 1,
            'lane_id': 1,
            'lane_no': 1,
            'company_id': 1,
            'company_name': 1,
            'provider_id': 1,
            'provider_name': 1,
            'loi_price': 1,
        }
        lane_providers = db.find(ProcurementDBColls.LANE_PROVIDERS, query, df)

        company_ids = set({rfq_doc['company_id']})
        lp_ids, lp_docs = [], []
        for lp_doc in lane_providers:
            company_ids.add(lp_doc['provider_id'])
            lp_ids.append(lp_doc['id'])
            lp_docs.append(lp_doc)

        total_lp_docs = len(lp_ids)
        if not lp_ids:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'No valid lane providers found for sending LOI.')

        # Send LOI
        update_query = {
            'loi_status_id': payload.loi_status_id,
            'loi_status': LOIStatus(payload.loi_status_id).name,
            'loi_start_time': payload.loi_start_time,
            'loi_end_time': payload.loi_end_time,
            'loi_instructions': payload.loi_instructions,
            'loi_remarks': payload.loi_remarks,
            'updated_on': request.now
        }

        db.update(ProcurementDBColls.LANE_PROVIDERS, {'id': {'$in': lp_ids}}, update_query, update_many=True)

        log_bulk_bid_trail(lp_docs, BiddingActions.LOI_SENT, request.ip, request.now)

        db.update(ProcurementDBColls.RFQ, {'id': rfq_doc['id']}, inc_query={'loi_sent': total_lp_docs})

        # Publish events
        publish_event(RedisChannel.AUCTION_UPDATES.value, rfq_ids=[rfq_id])
        publish_event(RedisChannel.BIDDING_STATUS.value, company_ids=list(company_ids))

        # Send bulk LOI notifications
        for lp_doc in lp_docs:
            try:
                notification_result = send_loi_notification(
                    rfq_id=lp_doc['rfq_id'],
                    lane_id=lp_doc['lane_id'],
                    provider_id=lp_doc['provider_id'],
                    rfq_no=rfq_doc.get('rfq_no'),
                    lane_no=lp_doc['lane_no'],
                    company_id=lp_doc['company_id'],
                    company_name=lp_doc['company_name'],
                    loi_price=lp_doc['loi_price'],
                    provider_name=lp_doc['provider_name'],
                    send_email=True,
                    send_sms=False
                )

                if not notification_result['email_sent']:
                    logger.warning(f"Failed to send bulk LOI notification for RFQ {lp_doc['rfq_id']}, Lane {lp_doc['lane_id']}, Provider {lp_doc['provider_id']}")

            except Exception as e:
                logger.error(f"Error sending bulk LOI notification for RFQ {lp_doc['rfq_id']}, Lane {lp_doc['lane_id']}, Provider {lp_doc['provider_id']}: {str(e)}")

        logger.info(f"Bulk LOI notifications processed for {total_lp_docs} lane providers")

        response_data = {
            'total_count': total_lp_docs,
        }
        return format_response(status.HTTP_200_OK, response_data, 'LOIs sent successfully')
