import re
import json
import redis
import hashlib
import xlsxwriter
from io import BytesIO
from django.conf import settings
from django.http import HttpResponse
from schema import BidTrailSchema, SettingsPayloadValidator
from utils.mongo import MongoUtility
from utils.constants import (
    AdminDBColls, ProcurementDBColls,
    BiddingActions, SAASModules,
    LongMIMETypes, BidCompetency,
    ServerSideEvent, BiddingSolution,
    RFQStatus, SAASProduct
)
from utils import DateUtil


def publish_event(channel: str, event_data: dict = None, company_ids: list = None, rfq_ids: list = None) -> None:
    r = redis.Redis.from_url(settings.REDIS_EVENTS_DB_URL)

    if not event_data:
        event_data = {'event': ServerSideEvent.UPDATE}

    if rfq_ids:
        for rfq_id in rfq_ids:
            r.publish(f'{channel}_{rfq_id}', json.dumps(event_data))
    elif company_ids:
        for company_id in company_ids:
            r.publish(f'{channel}_{company_id}', json.dumps(event_data))


def get_product_settings(company_id, product_id=SAASProduct.LOGISTICS_PROCUREMENT.value):
    db = MongoUtility(module_id=SAASModules.ADMIN.value)
    query = {'company_id': company_id, 'product_id': product_id}
    data = db.find(AdminDBColls.FEATURE_SETTINGS, query, find_one=True)
    if not data:
        data = SettingsPayloadValidator().model_dump()
    return data


def get_lanes_without_minimum_bidders_for_loi(rfq_id, min_bidders_for_loi):
    db = MongoUtility()

    pipeline = [
        {"$match": {"rfq_id": rfq_id, 'priority': {'$ne': None}}},
        {"$sort": {"lane_id": 1, "priority": -1}},
        {"$project": {"lane_no": 1, "lane_id": 1, "priority": 1}},
        {"$group": {"_id": "$lane_id", "lowest_rank_bid": {"$first": "$$ROOT"}}},
        {"$replaceRoot": {"newRoot": "$lowest_rank_bid"}}
    ]

    result = db.aggregate(ProcurementDBColls.LANE_PROVIDERS, pipeline=pipeline)
    invalid_lanes = []
    for item in result:
        if item['priority'] < min_bidders_for_loi:
            invalid_lanes.append(item)
    return invalid_lanes


def get_old_bid(lane_id, provider_id, previous_bid):
    db = MongoUtility()

    query = {'lane_id': lane_id, 'provider_id': provider_id}
    df = {'_id': 0, 'action': 1, 'price': 1}
    bid_history_docs = db.find(ProcurementDBColls.BID_TRAIL, query, df, sort=[('_id', -1)])

    old_bid = None
    for doc in bid_history_docs:
        if (doc['action'] == BiddingActions.BID_PLACED.value) and (doc['price'] > previous_bid):
            old_bid = doc['price']
            break
    return old_bid


def compute_savings_on_bid(ceiling_price, bid_price):
    try:
        savings_amt = ceiling_price - bid_price
        savings_percent = round((savings_amt / ceiling_price) * 100, 2)
    except TypeError:
        savings_amt, savings_percent = None, None
    return savings_amt, savings_percent


def get_bid_competency(rfq, lane, lane_provider, l1):
    # Its a measure of competition for L1 rank.
    # Only the first L1 hit per provider is considered to compute bid competency
    # i.e, the unique set of providers who got L1 rank atleast once

    was_l1 = lane_provider.get('was_l1', False)
    is_now_l1 = l1.get('provider_id') == lane_provider['provider_id']

    if was_l1 or (not is_now_l1):
        return {}

    total_providers = len(rfq['provider_ids'])
    previous_bid_comp_percent = lane['bid_comp_percent']

    bid_comp_percent = round(previous_bid_comp_percent + (100 / total_providers), 1)

    if bid_comp_percent >= 80:
        bid_comp = BidCompetency.HIGH.value
    elif bid_comp_percent >= 50:
        bid_comp = BidCompetency.MEDIUM.value
    else:
        bid_comp = BidCompetency.LOW.value

    return {
        'bid_comp': bid_comp,
        'bid_comp_percent': bid_comp_percent,
    }


def compute_other_bidding_stats(rfq, lane, lane_provider, l1, new_bid):
    # Stats computed here: l1_hits, plain_hits, participation, bid_comp, bid_comp_percent
    lane_update_query, lane_inc_query = {}, {}

    previous_bid = lane_provider['bid_price']
    total_providers = len(rfq['provider_ids'])

    if previous_bid:
        if not new_bid:
            # in case of withdrawal of all bids
            participation = round(lane.get('participation', 0) - (100 / total_providers), 1)
            lane_update_query['participation'] = participation
        elif new_bid < previous_bid:
            if l1.get('bid_price') == new_bid:
                lane_inc_query = {'l1_hits': 1}
            else:
                lane_inc_query = {'plain_hits': 1}
    else:
        participation = round(lane.get('participation', 0) + (100 / total_providers), 1)
        lane_update_query['participation'] = participation

    bid_comp_data = get_bid_competency(rfq, lane, lane_provider, l1)
    if bid_comp_data:
        lane_update_query.update(bid_comp_data)

    return lane_update_query, lane_inc_query


def update_bidding_ranks(rfq, lane_provider, update_all_ranks=False):
    db = MongoUtility()

    rfq_id = rfq['id']
    lane_id = lane_provider.get('lane_id')
    is_complete_solution = rfq['bidding_solution_id'] == BiddingSolution.COMPLETE.value
    primary_sort_key = 'bid_price' if rfq['rfq_status'] != RFQStatus.COMPLETED.value else 'loi_price'
    l1 = {}

    def get_sorted_lp_docs(query, primary_sort_key='bid_price'):
        df = {
            '_id': 0,
            'priority': 1,
            'lane_id': 1,
            'provider_id': 1,
            'bid_price': 1,
            'loi_price': 1,
            'updated_on': 1,
        }
        # Sort on bid_price by lowest first order.
        # (x['bid_price'] is None): Moves None values to the end.
        # x['bid_price']: Sorts in ascending order.
        # x['updated_on']: Ensures oldest first order when bid prices are the same.

        if primary_sort_key == 'loi_price':
            query['priority'] = {'$ne': None}

            lp_docs = list(db.find(ProcurementDBColls.LANE_PROVIDERS, query, df))
            lp_docs.sort(key=lambda x: (x[primary_sort_key], x['updated_on']))
        else:
            lp_docs = list(db.find(ProcurementDBColls.LANE_PROVIDERS, query, df))
            lp_docs.sort(key=lambda x: (x[primary_sort_key] is None, x[primary_sort_key], x['updated_on']))

        return lp_docs

    if is_complete_solution:
        query = {'rfq_id': rfq_id, 'bid_price': None}
        no_rank_providers = db.distinct(ProcurementDBColls.LANE_PROVIDERS, 'provider_id', query)

        lp_docs = get_sorted_lp_docs({'rfq_id': rfq_id}, primary_sort_key)

        lane_wise_ranked_providers_count = {}
        for lp_doc in lp_docs:
            lane_wise_ranked_providers_count.setdefault(lp_doc['lane_id'], 0)

            if lp_doc['provider_id'] in no_rank_providers:
                priority = None
            else:
                lane_wise_ranked_providers_count[lp_doc['lane_id']] += 1
                priority = lane_wise_ranked_providers_count[lp_doc['lane_id']]

            set_query = {}
            is_l1 = priority == 1
            if is_l1:
                set_query['was_l1'] = is_l1
                if lp_doc['lane_id'] == lane_id:
                    l1 = lp_doc

            if lp_doc['priority'] != priority:
                set_query['priority'] = priority

            if set_query:
                lp_query = {'lane_id': lp_doc['lane_id'], 'provider_id': lp_doc['provider_id']}
                db.add_write_operation(lp_query, set_query, update_one=True)
    else:
        query = {'rfq_id': rfq_id} if update_all_ranks else {'lane_id': lane_id}
        lp_docs = get_sorted_lp_docs(query, primary_sort_key)

        lane_wise_ranked_providers_count = {}
        for lp_doc in lp_docs:
            lane_wise_ranked_providers_count.setdefault(lp_doc['lane_id'], 0)

            if (not lp_doc[primary_sort_key]):
                priority = None
            else:
                lane_wise_ranked_providers_count[lp_doc['lane_id']] += 1
                priority = lane_wise_ranked_providers_count[lp_doc['lane_id']]

            set_query = {}
            is_l1 = priority == 1
            if is_l1:
                set_query['was_l1'] = is_l1
                if lp_doc['lane_id'] == lane_id:
                    l1 = lp_doc

            if lp_doc['priority'] != priority:
                set_query['priority'] = priority

            if set_query:
                lp_query = {'lane_id': lp_doc['lane_id'], 'provider_id': lp_doc['provider_id']}
                db.add_write_operation(lp_query, set_query, update_one=True)

    db.bulk_write(ProcurementDBColls.LANE_PROVIDERS)
    return l1


def generate_lpp_id(rfq, lane):
    company_id = lane['company_id']
    src_city = lane['src_city']
    dst_city = lane['dst_city']
    vehicle_type = lane['vehicle_type']
    vehicle_capacity = lane['vehicle_capacity']
    body_type = lane['body_type']
    pricing_basis = rfq['pricing_basis']

    combined = f"{company_id}|{src_city}|{dst_city}|{vehicle_type}|{vehicle_capacity}|{body_type}|{pricing_basis}".lower()
    clean_string = re.sub(r'[^a-z0-9|]', '', combined)
    hash_id = hashlib.md5(clean_string.encode()).hexdigest()  # 32-character hash
    return hash_id


def update_lpp(rfq, lane, lp_doc):
    is_l1 = lp_doc['priority'] == 1
    if not is_l1:
        return

    db = MongoUtility()

    now = DateUtil.get_current_timestamp()
    lpp_id = lane.get('lpp_id')

    query = {'id': lpp_id, 'company_id': lane['company_id']}

    if not lpp_id:
        lpp_id = generate_lpp_id(rfq, lane)
        doc = {}
    else:
        doc = db.find(ProcurementDBColls.LPP, query, find_one=True)

    if not doc:
        update_query = {
            'id': lpp_id,
            'lpp': lp_doc['loi_price'],
            'company_id': lane['company_id'],
            'src_city': lane['src_city'],
            'dst_city': lane['dst_city'],
            'vehicle_type': lane['vehicle_type'],
            'vehicle_type_id': lane['vehicle_type_id'],
            'vehicle_capacity': lane['vehicle_capacity'],
            'body_type': lane['body_type'],
            'body_type_id': lane['body_type_id'],
            'pricing_basis': rfq['pricing_basis'],
            'pricing_basis_id': rfq['pricing_basis_id'],
            'created_on': now,
            'updated_on': now
        }
    else:
        update_query = {
            'lpp': lp_doc['loi_price'],
            'updated_on': now
        }

    db.update(ProcurementDBColls.LPP, query, update_query, upsert=True)


def update_auction_savings(seeker_id, loi_price, ceiling_price):
    db = MongoUtility()
    now = DateUtil.get_current_timestamp()

    doc = db.find(ProcurementDBColls.AUCTION_SAVINGS, {'company_id': seeker_id}, find_one=True)

    update_query = {
        'total_accepted_lois': 0,
        'total_loi_price': 0,
        'total_ceiling_price': 0,
        'total_savings': 0,
        'total_savings_percent': 0,
        'avg_savings': 0,
        'avg_savings_percent': 0,
        'created_on': now
    }
    update_query.update(doc)
    update_query['updated_on'] = now

    update_query['total_accepted_lois'] += 1
    update_query['total_loi_price'] += loi_price
    update_query['total_ceiling_price'] += ceiling_price
    update_query['total_savings'] = update_query['total_ceiling_price'] - update_query['total_loi_price']
    update_query['total_savings_percent'] = round(update_query['total_savings'] * 100 / update_query['total_ceiling_price'])
    update_query['avg_savings'] = round(update_query['total_savings'] / update_query['total_accepted_lois'])

    previous_sum_savings_percent = update_query['avg_savings_percent'] * update_query['total_accepted_lois']
    current_loi_savings_percent = round((1 - (loi_price / ceiling_price)) * 100)
    update_query['avg_savings_percent'] = round((previous_sum_savings_percent + current_loi_savings_percent) / update_query['total_accepted_lois'])

    db.update(ProcurementDBColls.AUCTION_SAVINGS, {'company_id': seeker_id}, update_query, upsert=True)


def log_bulk_bid_trail(lp_docs: list, action: BiddingActions, ip: str, timestamp: int | None = None):
    bid_trail_docs = []
    for lp_doc in lp_docs:
        bid_trail_doc = log_bid_trail(lp_doc, BiddingActions.LOI_SENT, lp_doc['loi_price'], ip, timestamp, insert=False)
        bid_trail_docs.append(bid_trail_doc)

    if bid_trail_docs:
        db = MongoUtility()
        db.insert(ProcurementDBColls.BID_TRAIL, bid_trail_docs, insert_many=True)


def log_bid_trail(lp_doc: dict, action: BiddingActions, price: int, ip: str, timestamp: int | None = None, insert=True):
    obj = {
        'rfq_id': lp_doc['rfq_id'],
        'lane_id': lp_doc['lane_id'],
        'lane_no': lp_doc['lane_no'],
        'company_id': lp_doc['company_id'],
        'company_name': lp_doc['company_name'],
        'provider_id': lp_doc['provider_id'],
        'provider_name': lp_doc['provider_name'],
        'action': action.value,
        'price': price,
        'ip': ip,
    }
    if timestamp:
        obj['created_on'] = timestamp

    bid_trail_doc = BidTrailSchema(**obj).model_dump()

    if insert:
        db = MongoUtility()
        db.insert(ProcurementDBColls.BID_TRAIL, [bid_trail_doc])
    return bid_trail_doc


def download_bid_trail_excel(rfq_doc: dict, lane_docs: list[dict], bid_trail_docs: list[dict]) -> HttpResponse:
    if not bid_trail_docs:
        raise ValueError('No bid trail data found.')

    rfq_no = rfq_doc['rfq_no']

    # Create an in-memory Excel file
    output = BytesIO()
    workbook = xlsxwriter.Workbook(output, {'in_memory': True})
    bid_trail_worksheet = workbook.add_worksheet("Bid Trail")
    lane_worksheet = workbook.add_worksheet("Lane Details")
    rfq_worksheet = workbook.add_worksheet("RFQ Details")

    # Define headers for each worksheet
    bid_trail_headers = [
        'lane_id', 'lane_no',
        'company_name', 'provider_name',
        'action', 'price', 'ip', 'created_on'
    ]
    lane_headers = [
        'id', 'lane_no', 'vehicle_type', 'body_type',
        'src_state', 'src_state_code', 'src_city', 'src_address',
        'dst_state', 'dst_state_code', 'dst_city', 'dst_address',
        'rate', 'tat', 'tat_uom', 'stop_points'
    ]
    rfq_headers = [
        'rfq_no', 'company_name', 'bound_type',
        'service_mode', 'service_segment', 'service_type',
        'fuel_type', 'contract_start_date', 'contract_end_date',
        'insured_by', 'cargo_types', 'product_category', 'product_desc',
        'packing_type', 'pricing_basis', 'payment_terms',
        'bidding_start_time', 'bidding_end_time'
    ]

    worksheet_map = [
        [bid_trail_worksheet, bid_trail_headers, bid_trail_docs],
        [lane_worksheet, lane_headers, lane_docs],
        [rfq_worksheet, rfq_headers, [rfq_doc]],
    ]
    timestamp_fields = [
        'contract_start_date', 'contract_end_date',
        'bidding_start_time', 'bidding_end_time',
        'created_on'
    ]

    for worksheet, headers, data in worksheet_map:
        # Write headers to the first row
        for col_num, header in enumerate(headers):
            worksheet.write(0, col_num, header)

        # Write data rows
        for row_num, doc in enumerate(data, start=1):
            for col_num, key in enumerate(headers):
                value = doc.get(key, '')
                if key in timestamp_fields:
                    value = DateUtil.format_timestamp(value)
                elif key == 'stop_points':
                    value = len(value)
                elif key == 'cargo_types':
                    value = ','.join([x['name'] for x in (value or [])])
                worksheet.write(row_num, col_num, value)

    workbook.close()
    output.seek(0)

    # Create HTTP response for file download
    response = HttpResponse(output.read(), content_type=LongMIMETypes.SHEET)
    response['Content-Disposition'] = f'attachment; filename="{rfq_no}_bid_trail.xlsx"'
    return response
