from typing import Literal
from typing_extensions import Self
from pydantic import (
    BaseModel,
    Field,
    model_validator,
    field_validator
)
from utils import (
    DateUtil,
    get_uuid,
    Memoization
)
from utils.constants import (
    AdminDBColls,
    StopPointType,
    TatUOM,
    RFQStatus,
    BidStatus,
    LOIStatus
)
from schema import SelectedOptionsSchema, HandlingOptionsSchema

_START_TIME_BUFFER = 300000  # 5min
_MIN_BIDDING_DURATION = 900000  # 15min
_MIN_LOI_DURATION = 3600000  # 1hour
_MIN_CONTRACT_TENURE = 86400000  # 1day


class ListRFQsParamsValidator(BaseModel):
    rfq_status: int = Field(..., in_=[x.value for x in RFQStatus])
    limit: int = Field(default=10, gt=0, le=50)
    offset: int = Field(default=0, ge=0)
    sort_by: str = Field(default='created_on')
    sort_order: int = Field(default=-1, in_=[1, -1])
    search_term: str | None = None
    search_by: str | None = 'rfq_no'


class ListLanesParamsValidator(BaseModel):
    limit: int = Field(default=10, gt=0, le=300)
    offset: int = Field(default=0, ge=0)
    search_term: str | None = None


class CreateUpdateStopPointPayloadValidator(BaseModel):
    id: str = Field(default_factory=get_uuid)
    point_type: StopPointType
    place_id: str | None = ''
    place_name: str | None = ''
    lat: float | None = None
    lng: float | None = None
    address: str | None = ''
    landmark: str | None = ''
    pincode: int | None = None
    city: str | None = ''
    state: str | None = ''
    state_code: str | None = ''
    additional_charges: int | None = None

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class CreateUpdateLanePayloadValidator(BaseModel):
    id: str = Field(default_factory=get_uuid)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    placement_time: int | None = Field(..., description='vehicle placement datetime. used only in case of Spot contract')
    no_of_vehicles: int | None = Field(..., gt=0, description='number of vehicles. used only in case of Spot contract')
    vehicle_type: str
    vehicle_type_id: int
    vehicle_capacity: float = Field(..., gt=0, description='vehicle capacity in MT')
    body_type: str
    body_type_id: int
    src_place_id: str = Field(..., min_length=20)
    src_place_name: str | None = ''
    src_lat: float | None = None
    src_lng: float | None = None
    src_address: str
    src_landmark: str | None = ''
    src_pincode: int | None
    src_city: str | None
    src_state: str | None
    src_state_code: str | None
    dst_place_id: str = Field(..., min_length=20)
    dst_place_name: str | None = ''
    dst_lat: float | None = None
    dst_lng: float | None = None
    dst_address: str
    dst_landmark: str | None = ''
    dst_pincode: int | None
    dst_city: str | None
    dst_state: str | None
    dst_state_code: str | None
    stop_points: list[CreateUpdateStopPointPayloadValidator] | None = []
    lpp_id: str | None = None
    lpp: int | None = None
    rate: int
    distance: int = Field(..., gt=0)
    tat: int = Field(..., gt=0)
    tat_uom: TatUOM
    tat_uom_id: TatUOM
    volume: float
    volume_uom: str
    volume_uom_id: str
    advance_payment: int | None = Field(default=0, description='advance payment in percentage. used only in case of Spot contract')
    remarks: str | None = ''
    l1_rate: int | None = None
    l1_hits: int = Field(default=0)
    plain_hits: int = Field(default=0)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values

    @field_validator('placement_time', mode='after')
    def validate_placement_time(cls, value: int, values) -> int:
        # Access other field values from `values`
        current_time = values.data["updated_on"]
        if value and (value < current_time):
            raise ValueError("Placement date/time cannot be in the past.")
        return value

    @field_validator('rate', mode='after')
    def validate_rate(cls, value: int) -> int:
        if value <= 0:
            raise ValueError('Ceiling Price must be greater than zero.')
        return value

    # @model_validator(mode='after')
    def validate_state_fields(self) -> Self:
        states_list = Memoization.get_dropdown_data(AdminDBColls.STATES_LIST)
        is_src_state_validated, is_dst_state_validated = False, False
        for item in states_list:
            if not is_src_state_validated:
                if (self.src_state_code == item['id']) and (self.src_state == item['name']):
                    is_src_state_validated = True

            if not is_dst_state_validated:
                if (self.dst_state_code == item['id']) and (self.dst_state == item['name']):
                    is_dst_state_validated = True

            if is_src_state_validated and is_dst_state_validated:
                break

        if not is_src_state_validated:
            raise ValueError("Invalid Source State details.")

        if not is_dst_state_validated:
            raise ValueError("Invalid Destination State details.")
        return self


class UpdateRFQPayloadValidator(BaseModel):
    is_confirmed: bool = Field(default=False, description='signifies whether RFQ is confirmed/finalized and can be moved to "Active" bucket or just save the data')
    bound_type: str | None = ''
    bound_type_id: int | None = None
    service_mode: str | None = ''
    service_mode_id: int | None = None
    service_segment: str | None = ''
    service_segment_id: int | None = None
    service_type: str | None = ''
    service_type_id: int | None = None
    fuel_type: str | None = ''
    fuel_type_id: int | None = None
    contract_tenure: str | None = ''
    contract_tenure_id: int | None = None
    contract_start_date: int | None = None
    contract_end_date: int | None = None
    insured_by: str | None = ''
    insured_by_id: int | None = None
    cargo_types: list[SelectedOptionsSchema] | None = None
    handling_instructions: list[HandlingOptionsSchema] | None = None
    product_category: str | None = ''
    product_category_id: int | None = None
    product_desc: str | None = ''
    packing_type: str | None = ''
    packing_type_id: int | None = None
    bidding_solution: str | None = ''
    bidding_solution_id: int | None = None
    bidding_start_time: int | None = None
    bidding_end_time: int | None = None
    pricing_basis: str | None = ''
    pricing_basis_id: int | None = None
    payment_terms: str | None = ''
    payment_terms_id: int | None = None
    payment_cycle: str | None = ''
    payment_cycle_id: int | None = None
    advance_payment: int | None = Field(default=0, description='advance payment in percentage')
    provider_ids: list[str] | None = []
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    @model_validator(mode='after')
    def validate_timestamps(self) -> Self:
        current_time = self.updated_on
        current_time_with_buffer = current_time - _START_TIME_BUFFER
        if self.contract_start_date and self.contract_end_date:
            if self.contract_start_date > self.contract_end_date:
                raise ValueError("Contract start date cannot be greater the end date.")

            if self.contract_end_date < current_time:
                raise ValueError("Contract end date cannot be in the past.")

            if (self.contract_end_date - self.contract_start_date) < _MIN_CONTRACT_TENURE:
                raise ValueError("Contract tenure must be atleast 1day.")

        if self.bidding_start_time and self.bidding_end_time:
            if self.bidding_start_time > self.bidding_end_time:
                raise ValueError("Bidding start time cannot be greater the end time.")

            if self.bidding_start_time < current_time_with_buffer:
                raise ValueError("Bidding start time cannot be in the past.")

            if (self.bidding_end_time - self.bidding_start_time) < _MIN_BIDDING_DURATION:
                raise ValueError(f"Bidding duration must be atleast {_MIN_BIDDING_DURATION / 60000}min.")
        return self


class UpdateBidPayloadValidator(BaseModel):
    bid_price: int | None = Field(default=None, gt=0)
    bid_status_id: Literal[BidStatus.BIDDING.value, BidStatus.WITHDRAWN.value]
    bid_withdraw_reason: str | None = None

    @model_validator(mode='after')
    def conditional_validations(self) -> Self:
        if self.bid_status_id == BidStatus.BIDDING.value:
            if not self.bid_price:
                raise ValueError('Invalid Bid Price.')
        return self


class SendOrCancelLOIPayloadValidator(BaseModel):
    loi_status_id: Literal[LOIStatus.SENT.value, LOIStatus.CANCELLED.value]
    loi_start_time: int | None = None
    loi_end_time: int | None = None
    loi_instructions: str | None = None
    loi_remarks: str | None = None
    loi_cancel_reason: str | None = None

    @model_validator(mode='after')
    def conditional_validations(self) -> Self:
        current_time_with_buffer = DateUtil.get_current_timestamp() - _START_TIME_BUFFER
        if self.loi_status_id == LOIStatus.SENT.value:
            if not (self.loi_start_time and self.loi_end_time):
                raise ValueError("Please provide LOI valid from & to datetime.")

            if self.loi_start_time > self.loi_end_time:
                raise ValueError("LOI validity start time cannot be greater the end time.")

            if self.loi_start_time < current_time_with_buffer:
                raise ValueError("LOI validity start time cannot be in the past.")

            if (self.loi_end_time - self.loi_start_time) < _MIN_LOI_DURATION:
                raise ValueError("LOI must be valid for atleast 1hour.")
        elif self.loi_status_id == LOIStatus.CANCELLED.value:
            if not self.loi_cancel_reason:
                raise ValueError('Please mention the reason for cancelling LOI.')
        return self


class BulkLOIPayloadValidator(BaseModel):
    loi_status_id: Literal[LOIStatus.SENT.value, LOIStatus.CANCELLED.value]
    loi_start_time: int | None = None
    loi_end_time: int | None = None
    loi_instructions: str | None = None
    loi_remarks: str | None = None
    loi_cancel_reason: str | None = None

    # Different ways to specify which lanes to send LOI to
    priorities: list[int] | None = None
    lane_provider_ids: list[str] | None = None
    lane_ids: list[str] | None = None
    all_lanes: bool = False

    @model_validator(mode='after')
    def conditional_validations(self) -> Self:
        # Validate LOI fields based on status
        current_time_with_buffer = DateUtil.get_current_timestamp() - _START_TIME_BUFFER
        if self.loi_status_id == LOIStatus.SENT.value:
            if not (self.loi_start_time and self.loi_end_time):
                raise ValueError("Please provide LOI valid from & to datetime.")

            if self.loi_start_time > self.loi_end_time:
                raise ValueError("LOI validity start time cannot be greater the end time.")

            if self.loi_start_time < current_time_with_buffer:
                raise ValueError("LOI validity start time cannot be in the past.")

            if (self.loi_end_time - self.loi_start_time) < _MIN_LOI_DURATION:
                raise ValueError("LOI must be valid for atleast 1hour.")
        elif self.loi_status_id == LOIStatus.CANCELLED.value:
            if not self.loi_cancel_reason:
                raise ValueError('Please mention the reason for cancelling LOI.')

        # Validate that at least one selection method is provided
        selection_methods = [
            self.priorities is not None and len(self.priorities) > 0,
            self.lane_provider_ids is not None and len(self.lane_provider_ids) > 0,
            self.lane_ids is not None and len(self.lane_ids) > 0,
            self.all_lanes
        ]

        if not any(selection_methods):
            raise ValueError("Please provide at least one selection method: priorities, lane_provider_ids, lane_ids, or all_lanes.")

        # Validate that only one selection method is provided
        if sum(selection_methods) > 1:
            raise ValueError("Please provide only one selection method: priorities, lane_provider_ids, lane_ids, or all_lanes.")

        return self
