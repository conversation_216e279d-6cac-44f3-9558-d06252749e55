# SSE (Server Side Events) Views

import json
import time
import logging
import asyncio
from redis.asyncio import Redis
from sse_starlette import EventSourceResponse
from starlette.requests import Request
from rest_framework import status
from rest_framework.exceptions import AuthenticationFailed
from starlette.responses import JSONResponse
from django.conf import settings
from utils import (
    Memoization, ConnectionTimeLimitExceededError,
    JWTToken, format_response, format_error_response
)
from utils.constants import RedisChannel, ServerSideEvent
from authn import (
    AuthenticateAll,
    AuthenticateSeeker,
    AuthenticateProvider,
    AuthenticateSSE,
    PermissionedAPIView,
    Permissions
)
from .app_utils import publish_event

logger = logging.getLogger('application')

DEFAULT_SSE_HEARTBEAT_INTERVAL = 60
DEFAULT_SSE_CONNECTION_MAX_DURATION = 3600


class GetSSETokenAPI(PermissionedAPIView):
    """
    API to generate JWT tokens for SSE (Server-Sent Events) authentication.

    This API generates JWT tokens that can be used to authenticate SSE connections.
    The token includes user and company information for proper authorization.
    """
    authentication_classes = (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Authenti<PERSON><PERSON>rovider)
    permissions = [
        (Permissions.Procurement.VIEW, ['GET'])
    ]

    def get(self, request, *args, **kwargs):
        """
        Generate a JWT token for SSE authentication.

        Returns:
            JWT token with user and company information for SSE connections
        """
        sse_token = JWTToken.get_token(
            payload={
                'user_id': request.user_id,
                'company_id': request.company_id,
                'company_type': request.company_type,
            },
            subject=request.user_id,
            purpose=JWTToken.Purpose.SSE_EVENTS,
            expiry=JWTToken.Expiry.SSE_EVENTS
        )

        response_data = {
            'sse_token': sse_token,
            'expires_in': JWTToken.Expiry.SSE_EVENTS * 60,  # Convert minutes to seconds
        }
        return format_response(status.HTTP_200_OK, response_data, 'SSE token generated successfully')


def add_sse_response_headers(request, response):
    origin = request.headers.get('origin')
    if (origin in settings.CORS_ALLOWED_ORIGINS) or ('*' in settings.CORS_ALLOWED_ORIGINS):
        response.headers['Access-Control-Allow-Origin'] = origin
        response.headers['Access-Control-Allow-Credentials'] = str(settings.CORS_ALLOW_CREDENTIALS).lower()
    return response


def get_channel(channel, company_id=None, rfq_id=None):
    if rfq_id:
        return f"{channel}_{rfq_id}"
    elif company_id:
        return f"{channel}_{company_id}"
    else:
        raise ValueError('Mandatory: company_id or rfq_id')


class TokenExpiredError(Exception):
    """Custom exception for JWT token expiration."""
    pass


async def event_stream(channel: str, company_id=None, rfq_id=None, token_expires_at=None):
    """
    Enhanced event stream with JWT token expiration handling.

    Args:
        channel: Redis channel name
        company_id: Company ID for channel subscription
        rfq_id: RFQ ID for channel subscription
        token_expires_at: JWT token expiration timestamp
    """
    redis = Redis.from_url(settings.REDIS_EVENTS_DB_URL, decode_responses=True)
    pubsub = redis.pubsub()
    topic = get_channel(channel, company_id, rfq_id)
    await pubsub.subscribe(topic)

    buffer = None
    start_time = time.time()
    module_config = await Memoization.get_module_config_async()
    SSE_CONNECTION_MAX_DURATION = module_config.get('sse_connection_max_duration') or DEFAULT_SSE_CONNECTION_MAX_DURATION

    try:
        while True:
            current_time = time.time()

            # Check connection time limit
            if (current_time - start_time) > SSE_CONNECTION_MAX_DURATION:
                raise ConnectionTimeLimitExceededError

            # Check JWT token expiration
            if token_expires_at and current_time >= token_expires_at:
                logger.info(f"SSE connection closed: JWT token expired for company {company_id}")
                raise TokenExpiredError("JWT token has expired")

            message = await pubsub.get_message(timeout=1)

            if message and message['type'] == 'message':
                buffer = json.loads(message['data'])

            if buffer:
                yield {"data": json.dumps(buffer)}
                buffer = None

            await asyncio.sleep(1)
    except ConnectionTimeLimitExceededError:
        logger.info("SSE connection time limit exceeded")
        # Send a final message to client about connection timeout
        yield {
            "event": ServerSideEvent.CONNECTION_TIMEOUT,
            "data": json.dumps({
                "type": "connection_closed",
                "reason": "connection_timeout",
                "message": "Connection time limit exceeded"
            })
        }
    except TokenExpiredError:
        logger.info(f"SSE connection closed: JWT token expired for company {company_id}")
        # Send a final message to client about token expiration
        yield {
            "event": ServerSideEvent.TOKEN_EXPIRED,
            "data": json.dumps({
                "type": "connection_closed",
                "reason": "token_expired",
                "message": "JWT token has expired. Please refresh your token and reconnect."
            })
        }
    except asyncio.CancelledError:
        logger.info("Client disconnected")
    finally:
        logger.info("Closing SSE connection")
        await pubsub.unsubscribe(topic)
        await pubsub.close()
        await redis.close()


# Django ASGI view
async def bidding_status_sse_view(request: Request):
    try:
        AuthenticateSSE().authenticate(request)
    except AuthenticationFailed as e:
        return JSONResponse(content=e.detail, status_code=e.status_code)

    channel = RedisChannel.BIDDING_STATUS.value

    module_config = await Memoization.get_module_config_async()
    SSE_HEARTBEAT_INTERVAL = module_config.get('sse_heartbeat_interval') or DEFAULT_SSE_HEARTBEAT_INTERVAL

    # Pass token expiration time to event stream for auto-close functionality
    token_expires_at = getattr(request, 'expires_on', None)

    response = EventSourceResponse(
        event_stream(channel, company_id=request.company_id, token_expires_at=token_expires_at),
        ping=SSE_HEARTBEAT_INTERVAL
    )
    return add_sse_response_headers(request, response)


async def rfq_status_update_sse_view(request: Request):
    try:
        AuthenticateSSE().authenticate(request)
    except AuthenticationFailed as e:
        return JSONResponse(content=e.detail, status_code=e.status_code)

    rfq_id = request.path_params['rfq_id']
    channel = RedisChannel.RFQ_STATUS_UPDATE.value

    module_config = await Memoization.get_module_config_async()
    SSE_HEARTBEAT_INTERVAL = module_config.get('sse_heartbeat_interval') or DEFAULT_SSE_HEARTBEAT_INTERVAL

    # Pass token expiration time to event stream for auto-close functionality
    token_expires_at = getattr(request, 'expires_on', None)

    response = EventSourceResponse(
        event_stream(channel, rfq_id=rfq_id, token_expires_at=token_expires_at),
        ping=SSE_HEARTBEAT_INTERVAL
    )
    return add_sse_response_headers(request, response)


async def auction_updates_sse_view(request: Request):
    try:
        AuthenticateSSE().authenticate(request)
    except AuthenticationFailed as e:
        return JSONResponse(content=e.detail, status_code=e.status_code)

    rfq_id = request.path_params['rfq_id']
    channel = RedisChannel.AUCTION_UPDATES.value

    module_config = await Memoization.get_module_config_async()
    SSE_HEARTBEAT_INTERVAL = module_config.get('sse_heartbeat_interval') or DEFAULT_SSE_HEARTBEAT_INTERVAL

    # Pass token expiration time to event stream for auto-close functionality
    token_expires_at = getattr(request, 'expires_on', None)

    response = EventSourceResponse(
        event_stream(channel, rfq_id=rfq_id, token_expires_at=token_expires_at),
        ping=SSE_HEARTBEAT_INTERVAL
    )
    return add_sse_response_headers(request, response)


class TestEventsView(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.Procurement.VIEW, ['POST'])
    ]

    def post(self, request, *args, **kwargs):
        payload = request.data

        try:
            publish_event(payload['channel'], payload['data'], [request.company_id], payload.get('rfq_ids'))
        except KeyError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Missing key: {e}')

        return format_response(status.HTTP_200_OK, {}, 'Success')
