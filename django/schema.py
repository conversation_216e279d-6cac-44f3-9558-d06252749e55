from typing_extensions import Self
from typing import Type
from pydantic import BaseModel, Field, HttpUrl, model_validator
from utils import get_uuid, DateUtil, generate_rfq_no, make_optional
from utils.constants import (
    RFQStatus,
    StopPointType,
    TatUOM,
    BidStatus,
    ChallengeStatus,
    CounterStatus,
    LOIStatus,
    BiddingActions,
    BidCompetency,
    BiddingCategory,
    VendorBiddingScreen,
)


class HandlingInstr:
    TEMP_SENSITIVE = 2


class SelectedOptionsSchema(BaseModel):
    id: int
    name: str


class HandlingOptionsSchema(BaseModel):
    id: int
    name: str
    min_temp: int | None = Field(default=None, ge=-80, le=80)
    max_temp: int | None = Field(default=None, ge=-80, le=80)
    temp_uom: str | None = None
    temp_uom_id: str | None = None

    def model_dump(self, *args, **kwargs):
        # Force exclude_none=True only for this model
        kwargs.setdefault("exclude_none", True)
        return super().model_dump(*args, **kwargs)

    @model_validator(mode="after")
    def validate_temperature_fields(self) -> Self:
        if self.id != HandlingInstr.TEMP_SENSITIVE:
            return self

        is_min_set = self.min_temp is not None
        is_max_set = self.max_temp is not None
        is_uom_set = bool(self.temp_uom)

        fields_set = [is_min_set, is_max_set, is_uom_set]

        if not all(fields_set):
            raise ValueError("Missing temperature details in handling instructions.")

        if self.min_temp > self.max_temp:
            raise ValueError("Minimum temperature cannot be greater than Maximum temperature.")

        return self


class DraftRFQSchema(BaseModel):
    id: str = Field(default_factory=get_uuid)
    rfq_status: RFQStatus = Field(default=RFQStatus.DRAFT.value)
    company_id: str
    rfq_no: str | None = None  # Initialize as empty string; will be set dynamically
    company_name: str
    bound_type: str | None = ''
    bound_type_id: int | None = None
    service_mode: str | None = ''
    service_mode_id: int | None = None
    service_segment: str | None = ''
    service_segment_id: int | None = None
    service_type: str | None = ''
    service_type_id: int | None = None
    fuel_type: str | None = ''
    fuel_type_id: int | None = None
    contract_tenure: str | None = ''
    contract_tenure_id: int | None = None
    contract_start_date: int | None = None
    contract_end_date: int | None = None
    contract_note: str | None = ''
    insured_by: str | None = ''
    insured_by_id: int | None = None
    cargo_types: list[SelectedOptionsSchema] | None = None
    handling_instructions: list[HandlingOptionsSchema] | None = None
    product_category: str | None = ''
    product_category_id: int | None = None
    product_desc: str | None = None
    packing_type: str | None = ''
    packing_type_id: int | None = None
    bidding_solution: str | None = ''
    bidding_solution_id: int | None = None
    bidding_start_time: int | None = None
    bidding_end_time: int | None = None
    pricing_basis: str | None = ''
    pricing_basis_id: int | None = None
    payment_terms: str | None = ''
    payment_terms_id: int | None = None
    payment_cycle: str | None = ''
    payment_cycle_id: int | None = None
    advance_payment: int | None = None
    attachments: list | None = []
    provider_ids: list[str] | None = []
    chp_sent: int = Field(default=0, description='challenge price sent')
    chp_accepted: int = Field(default=0, description='challenge price accepted')
    chp_rejected: int = Field(default=0, description='challenge price rejected')
    cop_sent: int = Field(default=0, description='counter price sent')
    cop_accepted: int = Field(default=0, description='counter price accepted')
    cop_rejected: int = Field(default=0, description='counter price rejected')
    loi_sent: int = Field(default=0, description='LOI sent')
    loi_accepted: int = Field(default=0, description='LOI accepted')
    loi_rejected: int = Field(default=0, description='LOI rejected')
    loi_ignored: int = Field(default=0, description='LOI ignored')
    created_by_id: str
    created_by: str
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values

    @model_validator(mode="after")
    def set_rfq_no(self) -> Self:
        if not self.rfq_no:
            self.rfq_no = generate_rfq_no(self.company_id)
        return self


class RFQAttachmentSchema(BaseModel):
    id: str = Field(default_factory=get_uuid)
    file_name: str
    file_size: int
    type: str
    url: HttpUrl
    created_by_id: str
    created_by: str
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)


class RFQSchema(BaseModel):
    id: str
    rfq_no: str
    rfq_status: RFQStatus
    company_id: str
    company_name: str
    bound_type: str
    bound_type_id: int
    service_mode: str
    service_mode_id: int
    service_segment: str
    service_segment_id: int
    service_type: str
    service_type_id: int
    fuel_type: str
    fuel_type_id: int
    contract_tenure: str
    contract_tenure_id: int
    contract_start_date: int
    contract_end_date: int
    contract_note: str | None = None
    insured_by: str
    insured_by_id: int
    cargo_types: list[SelectedOptionsSchema]
    handling_instructions: list[HandlingOptionsSchema]
    product_category: str
    product_category_id: int
    product_desc: str = Field(..., description='product description i.e., material name')
    packing_type: str
    packing_type_id: int
    bidding_solution: str
    bidding_solution_id: int
    bidding_start_time: int
    bidding_end_time: int
    pricing_basis: str
    pricing_basis_id: int
    payment_terms: str
    payment_terms_id: int
    payment_cycle: str | None = None
    payment_cycle_id: int | None = None
    advance_payment: int | None = Field(default=0, description='advance payment in percentage')
    attachments: list[RFQAttachmentSchema]
    provider_ids: list[str]
    chp_sent: int = Field(default=0, description='challenge price sent')
    chp_accepted: int = Field(default=0, description='challenge price accepted')
    chp_rejected: int = Field(default=0, description='challenge price rejected')
    cop_sent: int = Field(default=0, description='counter price sent')
    cop_accepted: int = Field(default=0, description='counter price accepted')
    cop_rejected: int = Field(default=0, description='counter price rejected')
    loi_sent: int = Field(default=0, description='LOI sent')
    loi_accepted: int = Field(default=0, description='LOI accepted')
    loi_rejected: int = Field(default=0, description='LOI rejected')
    loi_ignored: int = Field(default=0, description='LOI ignored')
    created_by_id: str
    created_by: str
    created_on: int
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values

    @classmethod
    def configure_mandatory(cls, strict: bool) -> Type[BaseModel]:
        """Dynamically modify required fields in a nested Pydantic model."""
        return make_optional(cls, strict)


class RFQProviderSchema(BaseModel):
    rfq_id: str
    company_id: str
    provider_id: str
    provider_name: str
    rfq_seen_on: int | None = None
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)


class StopPointSchema(BaseModel):
    id: str
    point_type: StopPointType
    place_id: str | None = Field(default=None, min_length=20)
    place_name: str | None = ''
    lat: float | None = None
    lng: float | None = None
    address: str
    landmark: str | None = ''
    pincode: int
    city: str
    state: str
    state_code: str = Field(..., min_length=2, max_length=2)
    additional_charges: int | None = None

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class LaneSchema(BaseModel):
    id: str
    lane_no: int | None = None
    rfq_id: str
    company_id: str
    placement_time: int | None = Field(..., description='vehicle placement datetime. used only in case of Spot contract')
    no_of_vehicles: int | None = Field(..., gt=0, description='number of vehicles. used only in case of Spot contract')
    vehicle_type: str
    vehicle_type_id: int
    vehicle_capacity: float = Field(..., description='vehicle capacity in MT')
    body_type: str
    body_type_id: int
    src_place_id: str | None = Field(default=None, min_length=20)
    src_place_name: str | None = None
    src_lat: float | None = None
    src_lng: float | None = None
    src_address: str
    src_landmark: str | None = ''
    src_pincode: int
    src_city: str
    src_state: str
    src_state_code: str = Field(..., min_length=2, max_length=2)
    dst_place_id: str | None = Field(default=None, min_length=20)
    dst_place_name: str | None = ''
    dst_lat: float | None = None
    dst_lng: float | None = None
    dst_address: str
    dst_landmark: str | None = ''
    dst_pincode: int
    dst_city: str
    dst_state: str
    dst_state_code: str = Field(..., min_length=2, max_length=2)
    stop_points: list[StopPointSchema] | None = []
    participation: float = Field(default=0, description='percentage of participation in bidding in this lane i.e (providers_who_placed_bids / total_providers)')
    bid_comp_percent: float = Field(default=0, description='bid_comp_percent signifies the percentage of competetion for L1 position i.e (total_unique_l1_providers / total_providers)')
    bid_comp: BidCompetency | None = Field(default=None, description='bid_competency signifies the level of competetion for L1 position')
    lpp_id: str | None = None
    lpp: int | None = None
    rate: int = Field(..., gt=0)
    distance: int = Field(..., gt=0)
    tat: int = Field(..., gt=0)
    tat_uom: TatUOM
    tat_uom_id: TatUOM
    volume: float
    volume_uom: str
    volume_uom_id: str
    advance_payment: int | None = Field(default=0, description='advance payment in percentage. used only in case of Spot contract')
    remarks: str | None = ''
    l1_rate: int | None = None
    l1_hits: int = Field(default=0)
    plain_hits: int = Field(default=0)
    created_on: int
    updated_on: int

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class LaneProviderSchema(BaseModel):
    id: str = Field(default_factory=get_uuid)
    rfq_id: str
    lane_id: str
    lane_no: int
    company_id: str
    company_name: str
    provider_id: str
    provider_name: str
    is_tnc_accepted: bool = False
    bid_price: int | None = None
    ip: str | None = None
    priority: int | None = None
    bid_status_id: BidStatus | None = BidStatus.NO_BID.value
    bid_status: str | None = BidStatus.NO_BID.name
    savings_amt: float | None = None
    savings_percent: float | None = None
    bid_withdraw_reason: str | None = None
    was_l1: bool = False
    challenge_price: int | None = None
    challenge_status_id: ChallengeStatus | None = ChallengeStatus.NOT_SENT.value
    challenge_status: str | None = ChallengeStatus.NOT_SENT.name
    counter_price: int | None = None
    counter_status_id: CounterStatus | None = CounterStatus.NOT_SENT.value
    counter_status: str | None = CounterStatus.NOT_SENT.name
    loi_price: int | None = None
    loi_status_id: LOIStatus | None = LOIStatus.NOT_SENT.value
    loi_status: str | None = LOIStatus.NOT_SENT.name
    loi_start_time: int | None = None
    loi_end_time: int | None = None
    loi_instructions: str | None = None
    loi_remarks: str | None = None
    loi_cancel_reason: str | None = None
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class BidTrailSchema(BaseModel):
    rfq_id: str
    lane_id: str
    lane_no: int
    company_id: str
    company_name: str
    provider_id: str
    provider_name: str
    action: BiddingActions
    price: int
    ip: str
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class SettingsPayloadValidator(BaseModel):
    enable_tat_visibility: bool = False
    bidding_category: str = BiddingCategory.MONITORED.name.capitalize()
    bidding_category_id: BiddingCategory = BiddingCategory.MONITORED.value
    bid_decremental: int = 0
    bid_tolerance: int | None = None
    l1_rate_decremental: int = 0
    vendor_bidding_screen: str = VendorBiddingScreen.BLIND_BIDDING.name.replace('_', ' ').capitalize()
    vendor_bidding_screen_id: VendorBiddingScreen = VendorBiddingScreen.BLIND_BIDDING.value
    enable_chp_with_cop: bool = False
    min_bidders_for_loi: int = 0
    enable_loi_approval: bool = False
    auto_send_loi_to_l1: bool = False
    enable_sms: bool = False
    enable_email: bool = False
    enable_whatsapp: bool = False

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values
