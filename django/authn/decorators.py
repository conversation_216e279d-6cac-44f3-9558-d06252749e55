import json
import hashlib
from functools import wraps
from pydantic import ValidationError
from rest_framework import status
from rest_framework.response import Response
from django.core.cache import cache
from django.http import JsonResponse
from utils import format_error_response, AccessError
from utils.mongo import MongoUtility
from utils.constants import ProcurementDBColls


def cache_api_response(timeout=3600):  # Default: Cache for 1 hour
    """Decorator to cache API responses including user context"""
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Build a cache key based on the full URL and user ID
            query_params = request.GET.dict()  # Convert QueryDict to a normal dict
            sorted_params = json.dumps(query_params, sort_keys=True)  # Sort params to maintain consistency

            cache_key = hashlib.md5(f"{request.path}?{sorted_params}".encode()).hexdigest()

            # Check if response is cached
            cached_response = cache.get(cache_key)
            if cached_response:
                return Response(cached_response)  # Return cached response if found

            # Call the actual view and cache the response
            response = view_func(request, *args, **kwargs)

            # Cache the response if it's a valid JSON response
            if response.status_code == 200:
                cache.set(cache_key, response.data, timeout=timeout)
            return response
        return _wrapped_view
    return decorator


def validate_query_params(validator_class):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            try:
                query_params = request.GET.dict()
                validated_data = validator_class(**query_params)
                request.validated_params = validated_data
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def validate_request_payload(validator_class):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            try:
                payload = request.data
                validated_data = validator_class(**payload)
                request.validated_payload = validated_data
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def validate_data_access(items: list[str]):
    class DataAccessValidators:
        @staticmethod
        def validate_rfq_access(request, *args, **kwargs):
            db = MongoUtility()

            company_key = 'company_id' if request.is_seeker else 'provider_ids'
            rfq_query = {'id': kwargs['rfq_id'], company_key: request.company_id}

            rfq_doc = db.find(ProcurementDBColls.RFQ, rfq_query, find_one=True)
            if not rfq_doc:
                raise ValueError('RFQ not found.')

            request.rfq_doc = rfq_doc
            return True

        @staticmethod
        def validate_lane_access(request, *args, **kwargs):
            db = MongoUtility()

            lane_query = {'id': kwargs['lane_id'], 'rfq_id': kwargs['rfq_id'], 'company_id': request.company_id}

            lane_doc = db.find(ProcurementDBColls.LANES, lane_query, find_one=True)
            if not lane_doc:
                raise ValueError('RFQ Lane not found.')

            request.lane_doc = lane_doc
            return True

        @staticmethod
        def validate_lane_provider_access(request, *args, **kwargs):
            db = MongoUtility()

            query = {'rfq_id': kwargs['rfq_id']}

            try:
                query['lane_id'] = kwargs['lane_id']
            except KeyError:
                pass

            if request.is_seeker:
                query['company_id'] = request.company_id
                try:
                    query['provider_id'] = kwargs['provider_id']
                except KeyError:
                    pass
            else:
                if ('provider_id' in kwargs) and (request.company_id != kwargs['provider_id']):
                    raise AccessError('Access Denied.')

                query['provider_id'] = request.company_id

            lp_doc = db.find(ProcurementDBColls.LANE_PROVIDERS, query, find_one=True)
            if not lp_doc:
                raise ValueError('Lane provider not found.')

            request.lp_doc = lp_doc
            return True

    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(self, request, *args, **kwargs):
            for item in items:
                try:
                    getattr(DataAccessValidators, f'validate_{item}_access')(request, *args, **kwargs)
                except ValueError as e:
                    return format_error_response(status.HTTP_404_NOT_FOUND, str(e))
                except AccessError as e:
                    return format_error_response(status.HTTP_403_FORBIDDEN, str(e))

            return view_func(self, request, *args, **kwargs)
        return _wrapped_view
    return decorator


def check_subscription(required_features=None, usage_limit_key=None):
    """
    Decorator to enforce subscription restrictions.

    It checks that the user has the required features and usage count is within limits.
    It then calls the function, and if the function returns successfully, it increments
    the usage counter.
    """
    from myapp.subscription_service import get_subscription
    if required_features is None:
        required_features = []

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Determine user_id from Django request or background task kwargs.
            request = kwargs.get("request", None)
            if request:
                user = request.user
                user_id = getattr(user, "id", None)
            else:
                user_id = kwargs.get("user_id", None)

            if not user_id:
                response = {"error": "User ID missing"}
                return JsonResponse(response, status=403) if request else response

            subscription = get_subscription(user_id)
            if not subscription:
                response = {"error": "Invalid subscription plan"}
                return JsonResponse(response, status=403) if request else response

            # Enforce required features
            missing_features = [f for f in required_features if f not in subscription["features"]]
            if missing_features:
                response = {"error": f"Missing required features: {', '.join(missing_features)}"}
                return JsonResponse(response, status=403) if request else response

            # Optionally check usage count before calling the function.
            if usage_limit_key:
                limit = subscription["limits"].get(usage_limit_key)
                if limit is not None:
                    redis_key = f"{usage_limit_key}:{user_id}"
                    usage_count = cache.get(redis_key, 0)
                    if usage_count >= limit:
                        response = {"error": f"{usage_limit_key.replace('_', ' ').capitalize()} limit exceeded. Upgrade your plan."}
                        return JsonResponse(response, status=429) if request else response

            # Call the underlying function.
            result = func(*args, **kwargs)

            # Only after successful execution, increment the usage counter.
            if usage_limit_key:
                redis_key = f"{usage_limit_key}:{user_id}"
                cache.incr(redis_key)
                cache.expire(redis_key, 86400)  # Reset counter after 24 hours

            return result

        return wrapper
    return decorator
