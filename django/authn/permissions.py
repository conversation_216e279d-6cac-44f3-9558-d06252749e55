from rest_framework.views import APIView
from rest_framework.permissions import BasePermission


class Permissions:

    class Admin:
        VIEW = 'admin.view'
        ADD_USERS = 'admin.users.create'
        UPDATE_USERS = 'admin.users.update'
        DELETE_USERS = 'admin.users.delete'

    class Procurement:
        VIEW = 'procurement.view'
        CREATE_RFQ = 'procurement.rfq.create'
        UPDATE_RFQ = 'procurement.rfq.update'
        DELETE_RFQ = 'procurement.rfq.delete'
        CREATE_LANE = 'procurement.rfq.lane.create'
        UPDATE_LANE = 'procurement.rfq.lane.update'
        DELETE_LANE = 'procurement.rfq.lane.delete'
        CREATE_STOP_POINT = 'procurement.rfq.lane.stop_point.create'
        UPDATE_STOP_POINT = 'procurement.rfq.lane.stop_point.update'
        DELETE_STOP_POINT = 'procurement.rfq.lane.stop_point.delete'
        UPDATE_BID = 'procurement.rfq.lane.bid.update'
        CREATE_CHALLENGE_PRICE = 'procurement.rfq.lane.bid.challenge_price.create'
        RESPOND_CHALLENGE_PRICE = 'procurement.rfq.lane.bid.challenge_price.respond'
        CREATE_COUNTER_PRICE = 'procurement.rfq.lane.bid.counter_price.create'
        RESPOND_COUNTER_PRICE = 'procurement.rfq.lane.bid.counter_price.respond'
        CREATE_LOI = 'procurement.rfq.lane.bid.loi.create'
        UPDATE_LOI = 'procurement.rfq.lane.bid.loi.update'
        RESPOND_LOI = 'procurement.rfq.lane.bid.loi.respond'


class HasPermissions(BasePermission):

    def __init__(self, required_permissions):
        self.required_permissions = required_permissions

    def has_permission(self, request, view):
        # Skip permission checks for OPTIONS, allowing it unconditionally
        if request.method == 'OPTIONS':
            return True

        # If no specific permissions are mentioned for this method, deny access
        if not self.required_permissions:
            return False

        # Check if the user has all required permissions for the current method
        if hasattr(request, 'user_permissions'):
            return all(perm in request.user_permissions for perm in self.required_permissions)
        return False


class PermissionedAPIView(APIView):
    # Define permissions as a list of tuples with the format:
    # [('app_name.permission_code', ['METHOD1', 'METHOD2'])]
    permissions = []

    def get_permissions(self):
        # Filter permissions based on the request method
        required_permissions = [
            perm for perm, methods in self.permissions if self.request.method in methods
        ]
        # Return the permissions instance with the filtered permissions
        return [HasPermissions(required_permissions)]
