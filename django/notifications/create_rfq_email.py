import logging
from typing import Dict, List
from .base import BaseNotification
from utils.constants import AdminDBColls

logger = logging.getLogger('application')


class CreateRFQEmailNotification(BaseNotification):
    """
    Notification for Create RFQ email.

    Sends email to transporters and SMS to transporters when a new RFQ is created.
    """

    def __init__(self):
        super().__init__()
        self.notification_type = 'create_rfq_email'

    def get_email_subject(self, **kwargs) -> str:
        """Get the email subject for Create RFQ notification."""
        rfq_no = kwargs.get('rfq_no', 'N/A')
        company_name = kwargs.get('company_name', 'Company')

        return f"New RFQ Created - {rfq_no} from {company_name}"

    def get_email_content(self, **kwargs) -> str:
        """Get the email content for Create RFQ notification."""
        rfq_no = kwargs.get('rfq_no', 'N/A')
        company_name = kwargs.get('company_name', 'Company')
        rfq_title = kwargs.get('rfq_title', 'RFQ')
        bidding_start_time = kwargs.get('bidding_start_time', 'TBD')
        bidding_end_time = kwargs.get('bidding_end_time', 'TBD')
        total_lanes = kwargs.get('total_lanes', 0)

        content = f"""
        Dear Transporter,

        A new RFQ has been created and you have been invited to participate in the bidding process.

        RFQ Details:
        - RFQ Number: {rfq_no}
        - RFQ Title: {rfq_title}
        - Company: {company_name}
        - Total Lanes: {total_lanes}
        - Bidding Start Time: {bidding_start_time}
        - Bidding End Time: {bidding_end_time}

        Please log in to your portal to view the complete RFQ details and participate in the bidding.

        Best regards,
        SCLEN.AI Team
        """

        return content.strip()

    def get_sms_content(self, **kwargs) -> str:
        """Get the SMS content for Create RFQ notification."""
        rfq_no = kwargs.get('rfq_no', 'N/A')
        company_name = kwargs.get('company_name', 'Company')

        return f"New RFQ {rfq_no} from {company_name} is available for bidding. Login to view details and participate."

    def get_recipients(self, **kwargs) -> Dict[str, List[str]]:
        """
        Get the recipients for Create RFQ notification.

        Returns email and SMS recipients (transporters).
        """
        provider_ids = kwargs.get('provider_ids', [])

        email_recipients = []
        sms_recipients = []

        try:
            # Get transporter company details and their users
            for provider_id in provider_ids:
                # Get user details
                users = self.get_user_details(company_id=provider_id)
                for user in users:
                    if user.get('email'):
                        email_recipients.append(user['email'])
                    if user.get('mobile'):
                        sms_recipients.append(user['mobile'])

        except Exception as e:
            logger.error(f"Error getting recipients for Create RFQ notification: {str(e)}")

        return {
            'email_recipients': list(set(email_recipients)),  # Remove duplicates
            'sms_recipients': list(set(sms_recipients))
        }

    def run(self, send_email: bool = True, send_sms: bool = False,
            rfq_id: str = None, rfq_no: str = None, company_id: str = None,
            company_name: str = None, rfq_title: str = None,
            bidding_start_time: str = None, bidding_end_time: str = None,
            total_lanes: int = 0, provider_ids: List[str] = None,
            sms_template_id: str = 'CREATE_RFQ', **kwargs) -> Dict[str, bool]:
        """
        Run the Create RFQ email notification.

        Args:
            send_email: Whether to send email notification
            send_sms: Whether to send SMS notification
            rfq_id: RFQ ID
            rfq_no: RFQ number
            company_id: Seeker company ID
            company_name: Seeker company name
            rfq_title: RFQ title
            bidding_start_time: Bidding start time
            bidding_end_time: Bidding end time
            total_lanes: Total number of lanes
            provider_ids: List of transporter company IDs
            sms_template_id: SMS template ID

        Returns:
            Dict with 'email_sent' and 'sms_sent' status
        """
        # If RFQ details not provided, fetch from database
        if rfq_id and not all([rfq_no, company_name, rfq_title]):
            rfq_details = self.get_rfq_details(rfq_id)
            if rfq_details:
                rfq_no = rfq_no or rfq_details.get('rfq_no')
                company_id = company_id or rfq_details.get('company_id')
                rfq_title = rfq_title or rfq_details.get('rfq_title')
                bidding_start_time = bidding_start_time or rfq_details.get('bidding_start_time')
                bidding_end_time = bidding_end_time or rfq_details.get('bidding_end_time')
                provider_ids = provider_ids or rfq_details.get('provider_ids', [])

        # Get company name if not provided
        if company_id and not company_name:
            company_details = self.get_company_details(company_id)
            if company_details:
                company_name = company_details.get('company_name', 'Company')

        # Prepare user details for SMS
        user_details = {
            'company_id': company_id or '',
            'company_name': company_name or 'Company'
        }

        return super().run(
            send_email=send_email,
            send_sms=send_sms,
            rfq_no=rfq_no,
            company_name=company_name,
            rfq_title=rfq_title,
            bidding_start_time=bidding_start_time,
            bidding_end_time=bidding_end_time,
            total_lanes=total_lanes,
            provider_ids=provider_ids or [],
            user_details=user_details,
            sms_template_id=sms_template_id,
            **kwargs
        )
