#!/usr/bin/env python
"""
Test script for the notification system.

This script can be run to test the notification system without requiring
a full Django environment.
"""

import sys
import os

# Add the django directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_notification_imports():
    """Test that all notification classes can be imported."""
    print("Testing notification imports...")

    try:
        from notifications.base import BaseNotification
        print("✓ BaseNotification imported successfully")

        from notifications.create_rfq_email import CreateRFQEmailNotification
        print("✓ CreateRFQEmailNotification imported successfully")

        from notifications.send_loi import SendLOINotification
        print("✓ SendLOINotification imported successfully")

        from notifications.accept_loi import AcceptLOINotification
        print("✓ AcceptLOINotification imported successfully")

        from notifications.reject_loi import RejectLOINotification
        print("✓ RejectLOINotification imported successfully")

        from notifications.withdraw_bid import WithdrawBidNotification
        print("✓ WithdrawBidNotification imported successfully")

        from notifications.challenge_price import ChallengePriceNotification
        print("✓ ChallengePriceNotification imported successfully")

        from notifications.manager import NotificationManager
        print("✓ NotificationManager imported successfully")

        return True

    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False


def test_notification_manager():
    """Test the notification manager functionality."""
    print("\nTesting notification manager...")

    try:
        from notifications.manager import NotificationManager

        manager = NotificationManager()

        # Test available notifications
        available = manager.get_available_notifications()
        print(f"✓ Available notifications: {available}")

        # Test that all expected notifications are available
        expected = [
            'create_rfq_email',
            'send_loi',
            'accept_loi',
            'reject_loi',
            'withdraw_bid',
            'challenge_price'
        ]

        for notification_type in expected:
            if notification_type in available:
                print(f"✓ {notification_type} is available")
            else:
                print(f"✗ {notification_type} is missing")
                return False

        return True

    except Exception as e:
        print(f"✗ Manager test error: {e}")
        return False


def test_notification_classes():
    """Test individual notification classes."""
    print("\nTesting individual notification classes...")

    try:
        from notifications.create_rfq_email import CreateRFQEmailNotification
        from notifications.send_loi import SendLOINotification

        # Test CreateRFQEmailNotification
        create_rfq = CreateRFQEmailNotification()

        # Test method existence
        assert hasattr(create_rfq, 'get_email_subject'), "get_email_subject method missing"
        assert hasattr(create_rfq, 'get_email_content'), "get_email_content method missing"
        assert hasattr(create_rfq, 'get_sms_content'), "get_sms_content method missing"
        assert hasattr(create_rfq, 'get_recipients'), "get_recipients method missing"
        assert hasattr(create_rfq, 'run'), "run method missing"

        print("✓ CreateRFQEmailNotification methods exist")

        # Test SendLOINotification
        send_loi = SendLOINotification()

        assert hasattr(send_loi, 'get_email_subject'), "get_email_subject method missing"
        assert hasattr(send_loi, 'get_email_content'), "get_email_content method missing"
        assert hasattr(send_loi, 'get_sms_content'), "get_sms_content method missing"
        assert hasattr(send_loi, 'get_recipients'), "get_recipients method missing"
        assert hasattr(send_loi, 'run'), "run method missing"

        print("✓ SendLOINotification methods exist")

        return True

    except Exception as e:
        print(f"✗ Class test error: {e}")
        return False


def test_convenience_functions():
    """Test convenience functions."""
    print("\nTesting convenience functions...")

    try:
        from notifications.manager import (
            send_create_rfq_email,
            send_loi_notification,
            send_accept_loi_notification,
            send_reject_loi_notification,
            send_withdraw_bid_notification,
            send_challenge_price_notification
        )

        # Test that functions exist and are callable
        functions = [
            send_create_rfq_email,
            send_loi_notification,
            send_accept_loi_notification,
            send_reject_loi_notification,
            send_withdraw_bid_notification,
            send_challenge_price_notification
        ]

        for func in functions:
            assert callable(func), f"{func.__name__} is not callable"
            print(f"✓ {func.__name__} is callable")

        return True

    except Exception as e:
        print(f"✗ Convenience function test error: {e}")
        return False


def test_notification_content_generation():
    """Test notification content generation."""
    print("\nTesting notification content generation...")

    try:
        from notifications.create_rfq_email import CreateRFQEmailNotification

        notification = CreateRFQEmailNotification()

        # Test email subject generation
        subject = notification.get_email_subject(
            rfq_no='RFQ001',
            company_name='Test Company'
        )
        assert isinstance(subject, str), "Email subject should be a string"
        assert 'RFQ001' in subject, "RFQ number should be in subject"
        assert 'Test Company' in subject, "Company name should be in subject"
        print("✓ Email subject generation works")

        # Test email content generation
        content = notification.get_email_content(
            rfq_no='RFQ001',
            company_name='Test Company',
            rfq_title='Test RFQ',
            total_lanes=5
        )
        assert isinstance(content, str), "Email content should be a string"
        assert 'RFQ001' in content, "RFQ number should be in content"
        assert 'Test Company' in content, "Company name should be in content"
        print("✓ Email content generation works")

        # Test SMS content generation
        sms_content = notification.get_sms_content(
            rfq_no='RFQ001',
            company_name='Test Company'
        )
        assert isinstance(sms_content, str), "SMS content should be a string"
        assert 'RFQ001' in sms_content, "RFQ number should be in SMS content"
        print("✓ SMS content generation works")

        return True

    except Exception as e:
        print(f"✗ Content generation test error: {e}")
        return False


def run_all_tests():
    """Run all tests."""
    print("Running notification system tests...\n")

    tests = [
        test_notification_imports,
        test_notification_manager,
        test_notification_classes,
        test_convenience_functions,
        test_notification_content_generation
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ Test passed\n")
            else:
                print("✗ Test failed\n")
        except Exception as e:
            print(f"✗ Test error: {e}\n")

    print(f"Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The notification system is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
