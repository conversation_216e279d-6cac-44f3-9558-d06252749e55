import logging
from typing import Dict, List
from .base import BaseNotification
from utils.constants import AdminDBColls, ProcurementDBColls

logger = logging.getLogger('application')


class WithdrawBidNotification(BaseNotification):
    """
    Notification for Withdraw Bid.

    Sends email to transporters and seekers when a bid is withdrawn.
    """

    def __init__(self):
        super().__init__()
        self.notification_type = 'withdraw_bid'

    def get_email_subject(self, **kwargs) -> str:
        """Get the email subject for Withdraw Bid notification."""
        rfq_no = kwargs.get('rfq_no', 'N/A')
        lane_no = kwargs.get('lane_no', 'N/A')
        provider_name = kwargs.get('provider_name', 'Transporter')

        return f"Bid Withdrawn - RFQ {rfq_no}, Lane {lane_no} by {provider_name}"

    def get_email_content(self, **kwargs) -> str:
        """Get the email content for Withdraw Bid notification."""
        rfq_no = kwargs.get('rfq_no', 'N/A')
        lane_no = kwargs.get('lane_no', 'N/A')
        company_name = kwargs.get('company_name', 'Company')
        withdrawn_price = kwargs.get('withdrawn_price', 'N/A')
        source = kwargs.get('source', 'N/A')
        destination = kwargs.get('destination', 'N/A')
        provider_name = kwargs.get('provider_name', 'Transporter')
        withdrawal_reason = kwargs.get('withdrawal_reason', 'Not specified')

        content = f"""
        Dear Team,

        A bid has been withdrawn by the transporter for the following lane:

        Bid Withdrawal Details:
        - RFQ Number: {rfq_no}
        - Lane Number: {lane_no}
        - Transporter: {provider_name}
        - Route: {source} to {destination}
        - Withdrawn Bid Price: ₹{withdrawn_price}
        - Withdrawal Reason: {withdrawal_reason}

        The bidding rankings for this lane have been updated accordingly.

        Best regards,
        SCLEN.AI Team
        """

        return content.strip()

    def get_sms_content(self, **kwargs) -> str:
        """Get the SMS content for Withdraw Bid notification."""
        rfq_no = kwargs.get('rfq_no', 'N/A')
        lane_no = kwargs.get('lane_no', 'N/A')
        provider_name = kwargs.get('provider_name', 'Transporter')

        return f"Bid withdrawn by {provider_name} for RFQ {rfq_no}, Lane {lane_no}."

    def get_recipients(self, **kwargs) -> Dict[str, List[str]]:
        """
        Get the recipients for Withdraw Bid notification.

        Returns email and SMS recipients (seeker company users).
        """
        company_id = kwargs.get('company_id')  # Seeker company ID

        email_recipients = []
        sms_recipients = []

        try:
            if company_id:
                # Get users for the seeker company
                users = self.admin_db.find(
                    AdminDBColls.USERS,
                    {'company_id': company_id, 'is_active': True},
                    {'email': 1, 'mobile': 1}
                )

                for user in users:
                    if user.get('email'):
                        email_recipients.append(user['email'])
                    if user.get('mobile'):
                        sms_recipients.append(user['mobile'])

        except Exception as e:
            logger.error(f"Error getting recipients for Withdraw Bid notification: {str(e)}")

        return {
            'email_recipients': list(set(email_recipients)),
            'sms_recipients': list(set(sms_recipients))
        }

    def run(self, send_email: bool = True, send_sms: bool = False,
            rfq_id: str = None, lane_id: str = None, provider_id: str = None,
            rfq_no: str = None, lane_no: str = None, company_id: str = None,
            company_name: str = None, withdrawn_price: float = None,
            source: str = None, destination: str = None, provider_name: str = None,
            withdrawal_reason: str = None, sms_template_id: str = 'WITHDRAW_BID',
            **kwargs) -> Dict[str, bool]:
        """
        Run the Withdraw Bid notification.

        Args:
            send_email: Whether to send email notification
            send_sms: Whether to send SMS notification
            rfq_id: RFQ ID
            lane_id: Lane ID
            provider_id: Provider company ID
            rfq_no: RFQ number
            lane_no: Lane number
            company_id: Seeker company ID
            company_name: Seeker company name
            withdrawn_price: Withdrawn bid price
            source: Source location
            destination: Destination location
            provider_name: Provider company name
            withdrawal_reason: Reason for withdrawal
            sms_template_id: SMS template ID

        Returns:
            Dict with 'email_sent' and 'sms_sent' status
        """
        # Fetch missing details from database
        if rfq_id and not all([rfq_no, company_id]):
            rfq_details = self.get_rfq_details(rfq_id)
            if rfq_details:
                rfq_no = rfq_no or rfq_details.get('rfq_no')
                company_id = company_id or rfq_details.get('company_id')

        if lane_id and not all([lane_no, source, destination]):
            lane_details = self.get_lane_details(lane_id)
            if lane_details:
                lane_no = lane_no or lane_details.get('lane_no')
                source = source or lane_details.get('source')
                destination = destination or lane_details.get('destination')

        # Get company names if not provided
        if company_id and not company_name:
            company_details = self.get_company_details(company_id)
            if company_details:
                company_name = company_details.get('company_name', 'Company')

        if provider_id and not provider_name:
            provider_details = self.get_company_details(provider_id)
            if provider_details:
                provider_name = provider_details.get('company_name', 'Transporter')

        # Get withdrawn bid price from lane_providers if not provided
        if not withdrawn_price and rfq_id and lane_id and provider_id:
            try:
                lp_doc = self.db.find(
                    ProcurementDBColls.LANE_PROVIDERS,
                    {
                        'rfq_id': rfq_id,
                        'lane_id': lane_id,
                        'provider_id': provider_id
                    },
                    {'bid_price': 1},
                    find_one=True
                )
                if lp_doc:
                    withdrawn_price = lp_doc.get('bid_price')
            except Exception as e:
                logger.error(f"Error fetching withdrawn bid price: {str(e)}")

        # Prepare user details for SMS
        user_details = {
            'company_id': company_id or '',
            'company_name': company_name or 'Company'
        }

        return super().run(
            send_email=send_email,
            send_sms=send_sms,
            rfq_no=rfq_no,
            lane_no=lane_no,
            company_name=company_name,
            withdrawn_price=withdrawn_price,
            source=source,
            destination=destination,
            provider_name=provider_name,
            withdrawal_reason=withdrawal_reason,
            company_id=company_id,
            user_details=user_details,
            sms_template_id=sms_template_id,
            **kwargs
        )
