#!/usr/bin/env python
"""
Test script for notification email templates.

This script tests that all notification templates render correctly with sample data.
"""

import os
import sys
from datetime import datetime

# Add Django project to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_template_rendering():
    """Test that all notification templates can be rendered with sample data."""
    
    try:
        # Import Django template engine
        from django.template import Template, Context
        from django.template.loader import get_template
        from django.conf import settings
        
        # Configure Django settings if not already configured
        if not settings.configured:
            settings.configure(
                TEMPLATES=[{
                    'BACKEND': 'django.template.backends.django.DjangoTemplates',
                    'DIRS': [os.path.join(os.path.dirname(__file__), '..', 'templates')],
                    'APP_DIRS': True,
                    'OPTIONS': {
                        'context_processors': [
                            'django.template.context_processors.debug',
                            'django.template.context_processors.request',
                        ],
                    },
                }],
                USE_TZ=True,
            )
            
        import django
        django.setup()
        
        print("Testing notification email templates...\n")
        
        # Sample data for testing
        sample_data = {
            'create_rfq_email': {
                'message': {
                    'rfq_no': 'RFQ001',
                    'rfq_title': 'Transportation Services Q1 2024',
                    'company_name': 'ABC Logistics',
                    'total_lanes': 10,
                    'bidding_start_time': 1705305000000,  # Jan 15, 2024, 10:00 AM IST (epoch)
                    'bidding_end_time': 1705737000000,   # Jan 20, 2024, 6:00 PM IST (epoch)
                    'rfq_id': 'rfq_12345'
                }
            },
            'send_loi': {
                'message': {
                    'rfq_no': 'RFQ001',
                    'lane_no': 'L001',
                    'company_name': 'ABC Logistics',
                    'provider_name': 'XYZ Transport',
                    'source': 'Mumbai',
                    'destination': 'Delhi',
                    'loi_price': 50000,
                    'rfq_id': 'rfq_12345',
                    'lane_id': 'lane_456'
                }
            },
            'accept_loi': {
                'message': {
                    'rfq_no': 'RFQ001',
                    'lane_no': 'L001',
                    'provider_name': 'XYZ Transport',
                    'source': 'Mumbai',
                    'destination': 'Delhi',
                    'loi_price': 50000,
                    'rfq_id': 'rfq_12345'
                }
            },
            'reject_loi': {
                'message': {
                    'rfq_no': 'RFQ001',
                    'lane_no': 'L001',
                    'provider_name': 'XYZ Transport',
                    'source': 'Mumbai',
                    'destination': 'Delhi',
                    'loi_price': 50000,
                    'rejection_reason': 'Price too low for current market conditions',
                    'rfq_id': 'rfq_12345'
                }
            },
            'withdraw_bid': {
                'message': {
                    'rfq_no': 'RFQ001',
                    'lane_no': 'L001',
                    'provider_name': 'XYZ Transport',
                    'source': 'Mumbai',
                    'destination': 'Delhi',
                    'withdrawn_price': 55000,
                    'withdrawal_reason': 'Capacity constraints due to other commitments',
                    'rfq_id': 'rfq_12345',
                    'lane_id': 'lane_456'
                }
            },
            'challenge_price': {
                'message': {
                    'rfq_no': 'RFQ001',
                    'lane_no': 'L001',
                    'company_name': 'ABC Logistics',
                    'provider_name': 'XYZ Transport',
                    'source': 'Mumbai',
                    'destination': 'Delhi',
                    'challenge_price': 45000,
                    'current_bid': 50000,
                    'rfq_id': 'rfq_12345',
                    'lane_id': 'lane_456'
                }
            }
        }
        
        # Test each template
        templates_tested = 0
        templates_passed = 0
        
        for template_name, context_data in sample_data.items():
            try:
                print(f"Testing template: {template_name}.html")
                
                # Load and render template
                template = get_template(f'notifications/{template_name}.html')
                rendered_html = template.render(context_data)
                
                # Basic validation
                if len(rendered_html) > 1000:  # Should be substantial HTML
                    print(f"✓ {template_name}.html rendered successfully ({len(rendered_html)} chars)")
                    templates_passed += 1
                else:
                    print(f"✗ {template_name}.html rendered but seems too short")
                    
                templates_tested += 1
                
                # Optional: Save rendered HTML for manual inspection
                output_dir = os.path.join(os.path.dirname(__file__), 'test_output')
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)
                    
                with open(os.path.join(output_dir, f'{template_name}_test.html'), 'w') as f:
                    f.write(rendered_html)
                    
            except Exception as e:
                print(f"✗ {template_name}.html failed to render: {str(e)}")
                templates_tested += 1
                
        print(f"\nTemplate Test Results: {templates_passed}/{templates_tested} templates passed")
        
        if templates_passed == templates_tested:
            print("🎉 All templates rendered successfully!")
            return True
        else:
            print("❌ Some templates failed to render.")
            return False
            
    except ImportError as e:
        print(f"Django import error: {e}")
        print("Note: This test requires Django to be installed and configured.")
        return False
    except Exception as e:
        print(f"Template test error: {e}")
        return False


def test_template_files_exist():
    """Test that all expected template files exist."""
    print("Checking template file existence...\n")
    
    template_dir = os.path.join(os.path.dirname(__file__), '..', 'templates', 'notifications')
    expected_templates = [
        'create_rfq_email.html',
        'send_loi.html',
        'accept_loi.html',
        'reject_loi.html',
        'withdraw_bid.html',
        'challenge_price.html'
    ]
    
    files_found = 0
    total_files = len(expected_templates)
    
    for template_file in expected_templates:
        template_path = os.path.join(template_dir, template_file)
        if os.path.exists(template_path):
            print(f"✓ {template_file} exists")
            files_found += 1
        else:
            print(f"✗ {template_file} missing")
            
    print(f"\nFile Check Results: {files_found}/{total_files} template files found")
    
    if files_found == total_files:
        print("🎉 All template files exist!")
        return True
    else:
        print("❌ Some template files are missing.")
        return False


def test_template_content():
    """Test that templates contain expected content."""
    print("Checking template content...\n")
    
    template_dir = os.path.join(os.path.dirname(__file__), '..', 'templates', 'notifications')
    
    # Expected content patterns for each template
    content_checks = {
        'create_rfq_email.html': [
            'New RFQ',
            'rfq_no',
            'company_name',
            'bidding_start_time',
            'View RFQ'
        ],
        'send_loi.html': [
            'LOI Received',
            'Congratulations',
            'loi_price',
            'provider_name',
            'View LOI'
        ],
        'accept_loi.html': [
            'LOI Accepted',
            'accepted',
            'provider_name',
            'Confirmed Price'
        ],
        'reject_loi.html': [
            'LOI Rejected',
            'rejected',
            'rejection_reason',
            'alternative'
        ],
        'withdraw_bid.html': [
            'Bid Withdrawn',
            'withdrawn',
            'withdrawal_reason',
            'rankings'
        ],
        'challenge_price.html': [
            'Challenge Price',
            'challenge_price',
            'current_bid',
            'Accept Challenge'
        ]
    }
    
    templates_checked = 0
    templates_valid = 0
    
    for template_file, expected_content in content_checks.items():
        template_path = os.path.join(template_dir, template_file)
        
        if os.path.exists(template_path):
            try:
                with open(template_path, 'r') as f:
                    content = f.read()
                    
                missing_content = []
                for expected in expected_content:
                    if expected not in content:
                        missing_content.append(expected)
                        
                if not missing_content:
                    print(f"✓ {template_file} contains all expected content")
                    templates_valid += 1
                else:
                    print(f"✗ {template_file} missing content: {', '.join(missing_content)}")
                    
                templates_checked += 1
                
            except Exception as e:
                print(f"✗ {template_file} error reading file: {str(e)}")
                templates_checked += 1
        else:
            print(f"✗ {template_file} file not found")
            templates_checked += 1
            
    print(f"\nContent Check Results: {templates_valid}/{templates_checked} templates valid")
    
    if templates_valid == templates_checked:
        print("🎉 All templates contain expected content!")
        return True
    else:
        print("❌ Some templates are missing expected content.")
        return False


def run_all_tests():
    """Run all template tests."""
    print("Running notification template tests...\n")
    
    tests = [
        ("File Existence", test_template_files_exist),
        ("Content Validation", test_template_content),
        ("Template Rendering", test_template_rendering)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"Running {test_name} Test")
        print(f"{'='*50}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} test PASSED\n")
            else:
                print(f"❌ {test_name} test FAILED\n")
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {str(e)}\n")
            
    print(f"{'='*50}")
    print(f"Final Results: {passed_tests}/{total_tests} tests passed")
    print(f"{'='*50}")
    
    if passed_tests == total_tests:
        print("🎉 All template tests passed! Templates are ready for use.")
        return True
    else:
        print("❌ Some template tests failed. Please review the issues above.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
