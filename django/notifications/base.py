import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from django.conf import settings
from utils.send_email import send_email
from utils.send_sms import send_sms
from utils.mongo import MongoUtility
from utils.constants import AdminDBColls, ProcurementDBColls, SAASModules

logger = logging.getLogger('application')


class BaseNotification(ABC):
    """
    Base class for all notification types.

    This class provides common functionality for sending emails and SMS notifications
    with proper logging and error handling.
    """

    def __init__(self):
        """Initialize the base notification."""
        self.admin_db = MongoUtility(module_id=SAASModules.ADMIN.value)
        self.db = MongoUtility()
        self.notification_type = self.__class__.__name__

    @abstractmethod
    def get_email_subject(self, **kwargs) -> str:
        """Get the email subject for this notification type."""
        pass

    @abstractmethod
    def get_email_content(self, **kwargs) -> str:
        """Get the email content for this notification type."""
        pass

    @abstractmethod
    def get_sms_content(self, **kwargs) -> str:
        """Get the SMS content for this notification type."""
        pass

    @abstractmethod
    def get_recipients(self, **kwargs) -> Dict[str, List[str]]:
        """
        Get the recipients for this notification.

        Returns:
            Dict with keys 'email_recipients', 'sms_recipients'
        """
        pass

    def get_company_details(self, company_id: str) -> Optional[Dict[str, Any]]:
        """Get company details from database."""
        try:
            company = self.admin_db.find(
                AdminDBColls.COMPANIES,
                {'id': company_id},
                find_one=True
            )
            return company
        except Exception as e:
            logger.error(f"Error fetching company details for {company_id}: {str(e)}")
            return None

    def get_user_details(self, user_id: str = None, company_id: str = None) -> Optional[Dict[str, Any]]:
        """Get user details from database."""
        query = {'is_active': True}
        if user_id:
            query['id'] = user_id
        elif company_id:
            query['company_id'] = company_id
        else:
            raise ValueError('Either company_id or user_id must be provided.')

        try:
            users = self.admin_db.find(AdminDBColls.USERS, query)
            return users
        except Exception as e:
            logger.error(f"Error fetching user details for {user_id}: {str(e)}")
            return None

    def get_rfq_details(self, rfq_id: str) -> Optional[Dict[str, Any]]:
        """Get RFQ details from database."""
        try:
            rfq = self.db.find(
                ProcurementDBColls.RFQ,
                {'id': rfq_id},
                find_one=True
            )
            return rfq
        except Exception as e:
            logger.error(f"Error fetching RFQ details for {rfq_id}: {str(e)}")
            return None

    def get_lane_details(self, lane_id: str) -> Optional[Dict[str, Any]]:
        """Get lane details from database."""
        try:
            lane = self.db.find(
                ProcurementDBColls.LANES,
                {'id': lane_id},
                find_one=True
            )
            return lane
        except Exception as e:
            logger.error(f"Error fetching lane details for {lane_id}: {str(e)}")
            return None

    def send_email_notification(self, recipients: List[str], subject: str, content_data: dict,
                                cc_list: Optional[List[str]] = None,
                                attachments: Optional[List[str]] = None,
                                template: str = 'base.html',
                                template_context: Optional[Dict[str, Any]] = None) -> bool:
        """Send email notification."""
        try:
            if not recipients:
                logger.warning(f"No email recipients for {self.notification_type}")
                return False

            content_data['procurement_app_url'] = settings.PROCUREMENT_APP_URL
            send_email(
                receivers=recipients,
                ccs=cc_list or [],
                subject=subject,
                message=content_data,
                attachments=attachments,
                template=template,
            )
            logger.info(f"Email sent successfully for {self.notification_type} to {recipients}")
            return True

        except Exception as e:
            logger.error(f"Error sending email for {self.notification_type}: {str(e)}")
            return False

    def send_sms_notification(self, recipients: List[str], content: str,
                              user_details: Dict[str, Any], template_id: str,
                              message_params: Optional[Dict[str, Any]] = None) -> bool:
        """Send SMS notification."""
        try:
            if not recipients:
                logger.warning(f"No SMS recipients for {self.notification_type}")
                return False

            sms_details = {
                'receivers': recipients,
                'message': content,
                'user_details': user_details,
                'template_id': template_id,
                'new_template_ids': [],  # Add specific template IDs if needed
                'alert_type': self.notification_type,
                'message_params': message_params or {}
            }

            result = send_sms(sms_details)
            if result:
                logger.info(f"SMS sent successfully for {self.notification_type} to {recipients}")
            return result

        except Exception as e:
            logger.error(f"Error sending SMS for {self.notification_type}: {str(e)}")
            return False

    def run(self, send_email: bool = True, send_sms: bool = False, **kwargs) -> Dict[str, bool]:
        """
        Main method to run the notification.

        Args:
            send_email: Whether to send email notification
            send_sms: Whether to send SMS notification
            **kwargs: Notification-specific arguments

        Returns:
            Dict with 'email_sent' and 'sms_sent' status
        """
        try:
            logger.info(f"Running {self.notification_type} notification")

            # Get recipients
            recipients = self.get_recipients(**kwargs)
            email_recipients = recipients.get('email_recipients', [])
            sms_recipients = recipients.get('sms_recipients', [])

            results = {'email_sent': False, 'sms_sent': False}

            # Send email notification
            if send_email and email_recipients:
                subject = self.get_email_subject(**kwargs)
                content = self.get_email_content(**kwargs)
                content_data = {**kwargs}

                # Prepare template context with all notification data
                template_context = dict(kwargs)
                template_context.update({
                    'subject': subject,
                    'content': content
                })

                # Use notification-specific template
                template_name = f'notifications/{self.notification_type.lower()}.html'

                results['email_sent'] = self.send_email_notification(
                    email_recipients, subject, content_data,
                    template=template_name, template_context=template_context
                )

            # Send SMS notification
            if send_sms and sms_recipients:
                content = self.get_sms_content(**kwargs)
                # Get user details for SMS (required by SMS utility)
                user_details = kwargs.get('user_details', {
                    'company_id': kwargs.get('company_id', ''),
                    'company_name': kwargs.get('company_name', '')
                })
                template_id = kwargs.get('sms_template_id', 'default')

                results['sms_sent'] = self.send_sms_notification(
                    sms_recipients, content, user_details, template_id
                )

            logger.info(f"{self.notification_type} notification completed: {results}")
            return results

        except Exception as e:
            logger.error(f"Error running {self.notification_type} notification: {str(e)}")
            return {'email_sent': False, 'sms_sent': False}
