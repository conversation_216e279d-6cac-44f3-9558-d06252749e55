import logging
from typing import Dict
from .create_rfq_email import CreateRFQEmailNotification
from .send_loi import SendLOINotification
from .accept_loi import AcceptLOINotification
from .reject_loi import RejectLOINotification
from .withdraw_bid import WithdrawBidNotification
from .challenge_price import ChallengePriceNotification

logger = logging.getLogger('application')


class NotificationManager:
    """
    Manager class to easily access and send any notification.

    This class provides a centralized way to send notifications without
    having to import individual notification classes.
    """

    # Notification type constants
    CREATE_RFQ_EMAIL = 'create_rfq_email'
    SEND_LOI = 'send_loi'
    ACCEPT_LOI = 'accept_loi'
    REJECT_LOI = 'reject_loi'
    WITHDRAW_BID = 'withdraw_bid'
    CHALLENGE_PRICE = 'challenge_price'

    def __init__(self):
        """Initialize the notification manager."""
        self._notifications = {
            self.CREATE_RFQ_EMAIL: CreateRFQEmailNotification,
            self.SEND_LOI: SendLOINotification,
            self.ACCEPT_LOI: AcceptLOINotification,
            self.REJECT_LOI: RejectLOINotification,
            self.WITHDRAW_BID: WithdrawBidNotification,
            self.CHALLENGE_PRICE: ChallengePriceNotification,
        }

    def get_available_notifications(self) -> list:
        """Get list of available notification types."""
        return list(self._notifications.keys())

    def send_notification(self, notification_type: str, send_email: bool = True,
                          send_sms: bool = False, **kwargs) -> Dict[str, bool]:
        """
        Send a notification of the specified type.

        Args:
            notification_type: Type of notification to send
            send_email: Whether to send email notification
            send_sms: Whether to send SMS notification
            **kwargs: Notification-specific arguments

        Returns:
            Dict with 'email_sent' and 'sms_sent' status
        """
        try:
            if notification_type not in self._notifications:
                logger.error(f"Unknown notification type: {notification_type}")
                return {'email_sent': False, 'sms_sent': False}

            notification_class = self._notifications[notification_type]
            notification = notification_class()

            result = notification.run(send_email=send_email, send_sms=send_sms, **kwargs)
            logger.info(f"Notification {notification_type} sent: {result}")

            return result

        except Exception as e:
            logger.error(f"Error sending notification {notification_type}: {str(e)}")
            return {'email_sent': False, 'sms_sent': False}

    def send_create_rfq_email(self, send_email: bool = True, send_sms: bool = False,
                              **kwargs) -> Dict[str, bool]:
        """Send Create RFQ email notification."""
        return self.send_notification(self.CREATE_RFQ_EMAIL, send_email, send_sms, **kwargs)

    def send_loi_notification(self, send_email: bool = True, send_sms: bool = False,
                              **kwargs) -> Dict[str, bool]:
        """Send LOI notification."""
        return self.send_notification(self.SEND_LOI, send_email, send_sms, **kwargs)

    def send_accept_loi_notification(self, send_email: bool = True, send_sms: bool = False,
                                     **kwargs) -> Dict[str, bool]:
        """Send Accept LOI notification."""
        return self.send_notification(self.ACCEPT_LOI, send_email, send_sms, **kwargs)

    def send_reject_loi_notification(self, send_email: bool = True, send_sms: bool = False,
                                     **kwargs) -> Dict[str, bool]:
        """Send Reject LOI notification."""
        return self.send_notification(self.REJECT_LOI, send_email, send_sms, **kwargs)

    def send_withdraw_bid_notification(self, send_email: bool = True, send_sms: bool = False,
                                       **kwargs) -> Dict[str, bool]:
        """Send Withdraw Bid notification."""
        return self.send_notification(self.WITHDRAW_BID, send_email, send_sms, **kwargs)

    def send_challenge_price_notification(self, send_email: bool = True, send_sms: bool = False,
                                          **kwargs) -> Dict[str, bool]:
        """Send Challenge Price notification."""
        return self.send_notification(self.CHALLENGE_PRICE, send_email, send_sms, **kwargs)


# Convenience functions for easy access
def send_create_rfq_email(**kwargs) -> Dict[str, bool]:
    """Convenience function to send Create RFQ email notification."""
    manager = NotificationManager()
    return manager.send_create_rfq_email(**kwargs)


def send_loi_notification(**kwargs) -> Dict[str, bool]:
    """Convenience function to send LOI notification."""
    manager = NotificationManager()
    return manager.send_loi_notification(**kwargs)


def send_accept_loi_notification(**kwargs) -> Dict[str, bool]:
    """Convenience function to send Accept LOI notification."""
    manager = NotificationManager()
    return manager.send_accept_loi_notification(**kwargs)


def send_reject_loi_notification(**kwargs) -> Dict[str, bool]:
    """Convenience function to send Reject LOI notification."""
    manager = NotificationManager()
    return manager.send_reject_loi_notification(**kwargs)


def send_withdraw_bid_notification(**kwargs) -> Dict[str, bool]:
    """Convenience function to send Withdraw Bid notification."""
    manager = NotificationManager()
    return manager.send_withdraw_bid_notification(**kwargs)


def send_challenge_price_notification(**kwargs) -> Dict[str, bool]:
    """Convenience function to send Challenge Price notification."""
    manager = NotificationManager()
    return manager.send_challenge_price_notification(**kwargs)


# Example usage:
"""
# Using the manager
manager = NotificationManager()

# Send Create RFQ email notification
result = manager.send_create_rfq_email(
    rfq_id='rfq_123',
    rfq_no='RFQ001',
    company_name='ABC Company',
    rfq_title='Transportation Services',
    bidding_start_time='2024-01-15 10:00:00',
    bidding_end_time='2024-01-20 18:00:00',
    total_lanes=5,
    provider_ids=['provider_1', 'provider_2'],
    send_email=True,
    send_sms=True
)

# Send LOI notification
result = manager.send_loi_notification(
    rfq_id='rfq_123',
    lane_id='lane_456',
    provider_id='provider_1',
    loi_price=50000,
    send_email=True,
    send_sms=False
)

# Using convenience functions
result = send_accept_loi_notification(
    rfq_id='rfq_123',
    lane_id='lane_456',
    provider_id='provider_1'
)

result = send_challenge_price_notification(
    rfq_id='rfq_123',
    lane_id='lane_456',
    provider_id='provider_1',
    challenge_price=45000,
    current_bid=50000
)
"""
