# Notification System Integration Guide

This guide shows how to integrate the notification system with existing procurement views.

## Quick Start

```python
from notifications import NotificationManager

# Initialize the manager
manager = NotificationManager()

# Send any notification
result = manager.send_create_rfq_email(
    rfq_id='rfq_123',
    provider_ids=['provider_1', 'provider_2']
)
```

## Integration Points

### 1. RFQ Creation (rfq_views.py)

**In `CreateDraftRFQAPI.post()` method:**

```python
from notifications.manager import send_create_rfq_email

class CreateDraftRFQAPI(PermissionedAPIView):
    def post(self, request, *args, **kwargs):
        # ... existing RFQ creation logic ...
        
        # After RFQ is created successfully
        if created_rfq and created_rfq.get('provider_ids'):
            try:
                # Send Create RFQ notification
                notification_result = send_create_rfq_email(
                    rfq_id=created_rfq['id'],
                    rfq_no=created_rfq['rfq_no'],
                    company_id=created_rfq['company_id'],
                    rfq_title=created_rfq.get('rfq_title', ''),
                    bidding_start_time=created_rfq.get('bidding_start_time'),
                    bidding_end_time=created_rfq.get('bidding_end_time'),
                    provider_ids=created_rfq['provider_ids'],
                    send_email=True,
                    send_sms=True
                )
                
                if notification_result['email_sent']:
                    logger.info(f"RFQ creation notification sent for {created_rfq['id']}")
                    
            except Exception as e:
                logger.error(f"Failed to send RFQ creation notification: {str(e)}")
        
        # ... rest of the method ...
```

### 2. LOI Management (bidding_views.py)

**In `CreateUpdateLOIAPI.post()` method:**

```python
from notifications.manager import send_loi_notification

class CreateUpdateLOIAPI(PermissionedAPIView):
    def post(self, request, *args, **kwargs):
        # ... existing LOI creation logic ...
        
        # After LOI is sent successfully
        if loi_sent_successfully:
            try:
                # Send LOI notification
                notification_result = send_loi_notification(
                    rfq_id=kwargs['rfq_id'],
                    lane_id=kwargs['lane_id'],
                    provider_id=kwargs['provider_id'],
                    send_email=True,
                    send_sms=False  # As per requirements table
                )
                
                if notification_result['email_sent']:
                    logger.info(f"LOI notification sent for {kwargs['rfq_id']}/{kwargs['lane_id']}/{kwargs['provider_id']}")
                    
            except Exception as e:
                logger.error(f"Failed to send LOI notification: {str(e)}")
        
        # ... rest of the method ...
```

**In `LOIResponseAPI.post()` method:**

```python
from notifications.manager import send_accept_loi_notification, send_reject_loi_notification

class LOIResponseAPI(PermissionedAPIView):
    def post(self, request, *args, **kwargs):
        # ... existing LOI response logic ...
        
        is_accepted = payload.response  # True for accept, False for reject
        
        # After LOI response is processed successfully
        try:
            if is_accepted:
                # Send Accept LOI notification
                notification_result = send_accept_loi_notification(
                    rfq_id=kwargs['rfq_id'],
                    lane_id=kwargs['lane_id'],
                    provider_id=kwargs['provider_id'],
                    send_email=True,
                    send_sms=False
                )
                action = "accepted"
            else:
                # Send Reject LOI notification
                notification_result = send_reject_loi_notification(
                    rfq_id=kwargs['rfq_id'],
                    lane_id=kwargs['lane_id'],
                    provider_id=kwargs['provider_id'],
                    rejection_reason=payload.get('reason', 'Not specified'),
                    send_email=True,
                    send_sms=False
                )
                action = "rejected"
            
            if notification_result['email_sent']:
                logger.info(f"LOI {action} notification sent successfully")
                
        except Exception as e:
            logger.error(f"Failed to send LOI response notification: {str(e)}")
        
        # ... rest of the method ...
```

### 3. Bid Withdrawal (bidding_views.py)

**In `UpdateBidAPI.post()` method (when bid is withdrawn):**

```python
from notifications.manager import send_withdraw_bid_notification

class UpdateBidAPI(PermissionedAPIView):
    def post(self, request, *args, **kwargs):
        # ... existing bid update logic ...
        
        # Check if this is a bid withdrawal
        if payload.bid_status == BidStatus.WITHDRAWN.value:
            try:
                # Send Withdraw Bid notification
                notification_result = send_withdraw_bid_notification(
                    rfq_id=kwargs['rfq_id'],
                    lane_id=kwargs['lane_id'],
                    provider_id=request.company_id,
                    withdrawn_price=lp_doc.get('bid_price'),
                    withdrawal_reason=payload.get('withdrawal_reason', 'Not specified'),
                    send_email=True,
                    send_sms=True  # As per requirements table
                )
                
                if notification_result['email_sent']:
                    logger.info(f"Bid withdrawal notification sent for {kwargs['rfq_id']}/{kwargs['lane_id']}")
                    
            except Exception as e:
                logger.error(f"Failed to send bid withdrawal notification: {str(e)}")
        
        # ... rest of the method ...
```

### 4. Challenge Price (bidding_views.py)

**In `SendChallengePriceAPI.post()` method:**

```python
from notifications.manager import send_challenge_price_notification

class SendChallengePriceAPI(PermissionedAPIView):
    def post(self, request, *args, **kwargs):
        # ... existing challenge price logic ...
        
        # After challenge price is sent successfully
        try:
            # Send Challenge Price notification
            notification_result = send_challenge_price_notification(
                rfq_id=kwargs['rfq_id'],
                lane_id=kwargs['lane_id'],
                provider_id=kwargs['provider_id'],
                challenge_price=payload.challenge_price,
                current_bid=lp_doc.get('bid_price'),
                send_email=True,
                send_sms=False  # As per requirements table
            )
            
            if notification_result['email_sent']:
                logger.info(f"Challenge price notification sent for {kwargs['rfq_id']}/{kwargs['lane_id']}/{kwargs['provider_id']}")
                
        except Exception as e:
            logger.error(f"Failed to send challenge price notification: {str(e)}")
        
        # ... rest of the method ...
```

## Bulk Operations

For bulk operations like `BulkSendLOIAPI`, you can send notifications in batch:

```python
from notifications.manager import send_loi_notification

class BulkSendLOIAPI(PermissionedAPIView):
    def post(self, request, *args, **kwargs):
        # ... existing bulk LOI logic ...
        
        # After bulk LOI operation
        for lp_doc in successfully_sent_lois:
            try:
                send_loi_notification(
                    rfq_id=lp_doc['rfq_id'],
                    lane_id=lp_doc['lane_id'],
                    provider_id=lp_doc['provider_id'],
                    send_email=True,
                    send_sms=False
                )
            except Exception as e:
                logger.error(f"Failed to send bulk LOI notification: {str(e)}")
```

## Error Handling Best Practices

1. **Always wrap notification calls in try-except blocks**
2. **Don't let notification failures affect the main business logic**
3. **Log notification results for debugging**
4. **Use appropriate log levels (info for success, error for failures)**

```python
try:
    notification_result = send_loi_notification(...)
    
    if notification_result['email_sent']:
        logger.info("Notification sent successfully")
    else:
        logger.warning("Notification failed to send")
        
except Exception as e:
    logger.error(f"Notification system error: {str(e)}")
    # Continue with main business logic
```

## Configuration

You can control notification behavior through parameters:

```python
# Send only email
send_loi_notification(send_email=True, send_sms=False, ...)

# Send only SMS
send_create_rfq_email(send_email=False, send_sms=True, ...)

# Send both
send_withdraw_bid_notification(send_email=True, send_sms=True, ...)

# Custom SMS template
send_loi_notification(sms_template_id='CUSTOM_LOI_TEMPLATE', ...)
```

## Testing

Test notifications in development:

```python
# In development/testing, you might want to disable actual sending
if settings.DEBUG:
    # Log instead of sending
    logger.info(f"Would send notification: {notification_type}")
else:
    # Send actual notifications
    result = send_notification(...)
```

## Monitoring

Monitor notification success rates:

```python
# Track notification metrics
notification_result = send_loi_notification(...)

if notification_result['email_sent']:
    # Increment success counter
    pass
else:
    # Increment failure counter
    # Alert administrators if failure rate is high
    pass
```
