import logging
from typing import Dict, List
from .base import BaseNotification
from utils.constants import AdminDBColls, ProcurementDBColls

logger = logging.getLogger('application')


class ChallengePriceNotification(BaseNotification):
    """
    Notification for Challenge Price.

    Sends email to transporters when challenge price is sent.
    """

    def __init__(self):
        super().__init__()
        self.notification_type = 'challenge_price'

    def get_email_subject(self, **kwargs) -> str:
        """Get the email subject for Challenge Price notification."""
        rfq_no = kwargs.get('rfq_no', 'N/A')
        lane_no = kwargs.get('lane_no', 'N/A')
        company_name = kwargs.get('company_name', 'Company')

        return f"Challenge Price - RFQ {rfq_no}, Lane {lane_no} from {company_name}"

    def get_email_content(self, **kwargs) -> str:
        """Get the email content for Challenge Price notification."""
        rfq_no = kwargs.get('rfq_no', 'N/A')
        lane_no = kwargs.get('lane_no', 'N/A')
        company_name = kwargs.get('company_name', 'Company')
        challenge_price = kwargs.get('challenge_price', 'N/A')
        current_bid = kwargs.get('current_bid', 'N/A')
        source = kwargs.get('source', 'N/A')
        destination = kwargs.get('destination', 'N/A')
        provider_name = kwargs.get('provider_name', 'Transporter')

        content = f"""
        Dear {provider_name},

        You have received a challenge price for the following lane:

        Challenge Price Details:
        - RFQ Number: {rfq_no}
        - Lane Number: {lane_no}
        - Company: {company_name}
        - Route: {source} to {destination}
        - Your Current Bid: ₹{current_bid}
        - Challenge Price: ₹{challenge_price}

        Please review the challenge price and respond within the specified time frame. You can either accept or reject this challenge price.

        Best regards,
        SCLEN.AI Team
        """

        return content.strip()

    def get_sms_content(self, **kwargs) -> str:
        """Get the SMS content for Challenge Price notification."""
        rfq_no = kwargs.get('rfq_no', 'N/A')
        lane_no = kwargs.get('lane_no', 'N/A')
        challenge_price = kwargs.get('challenge_price', 'N/A')

        return f"Challenge price ₹{challenge_price} received for RFQ {rfq_no}, Lane {lane_no}. Login to respond."

    def get_recipients(self, **kwargs) -> Dict[str, List[str]]:
        """
        Get the recipients for Challenge Price notification.

        Returns email recipients (specific transporter).
        """
        provider_id = kwargs.get('provider_id')

        email_recipients = []
        sms_recipients = []

        try:
            if provider_id:
                # Get users for this specific provider company
                users = self.admin_db.find(
                    AdminDBColls.USERS,
                    {'company_id': provider_id, 'is_active': True},
                    {'email': 1, 'mobile': 1}
                )

                for user in users:
                    if user.get('email'):
                        email_recipients.append(user['email'])
                    if user.get('mobile'):
                        sms_recipients.append(user['mobile'])

        except Exception as e:
            logger.error(f"Error getting recipients for Challenge Price notification: {str(e)}")

        return {
            'email_recipients': list(set(email_recipients)),
            'sms_recipients': list(set(sms_recipients))
        }

    def run(self, send_email: bool = True, send_sms: bool = False,
            rfq_id: str = None, lane_id: str = None, provider_id: str = None,
            rfq_no: str = None, lane_no: str = None, company_id: str = None,
            company_name: str = None, challenge_price: float = None,
            current_bid: float = None, source: str = None, destination: str = None,
            provider_name: str = None, sms_template_id: str = 'CHALLENGE_PRICE',
            **kwargs) -> Dict[str, bool]:
        """
        Run the Challenge Price notification.

        Args:
            send_email: Whether to send email notification
            send_sms: Whether to send SMS notification
            rfq_id: RFQ ID
            lane_id: Lane ID
            provider_id: Provider company ID
            rfq_no: RFQ number
            lane_no: Lane number
            company_id: Seeker company ID
            company_name: Seeker company name
            challenge_price: Challenge price
            current_bid: Current bid price
            source: Source location
            destination: Destination location
            provider_name: Provider company name
            sms_template_id: SMS template ID

        Returns:
            Dict with 'email_sent' and 'sms_sent' status
        """
        # Fetch missing details from database
        if rfq_id and not all([rfq_no, company_id]):
            rfq_details = self.get_rfq_details(rfq_id)
            if rfq_details:
                rfq_no = rfq_no or rfq_details.get('rfq_no')
                company_id = company_id or rfq_details.get('company_id')

        if lane_id and not all([lane_no, source, destination]):
            lane_details = self.get_lane_details(lane_id)
            if lane_details:
                lane_no = lane_no or lane_details.get('lane_no')
                source = source or lane_details.get('source')
                destination = destination or lane_details.get('destination')

        # Get company names if not provided
        if company_id and not company_name:
            company_details = self.get_company_details(company_id)
            if company_details:
                company_name = company_details.get('company_name', 'Company')

        if provider_id and not provider_name:
            provider_details = self.get_company_details(provider_id)
            if provider_details:
                provider_name = provider_details.get('company_name', 'Transporter')

        # Get challenge price and current bid from lane_providers if not provided
        if (not challenge_price or not current_bid) and rfq_id and lane_id and provider_id:
            try:
                lp_doc = self.db.find(
                    ProcurementDBColls.LANE_PROVIDERS,
                    {
                        'rfq_id': rfq_id,
                        'lane_id': lane_id,
                        'provider_id': provider_id
                    },
                    {'challenge_price': 1, 'bid_price': 1},
                    find_one=True
                )
                if lp_doc:
                    challenge_price = challenge_price or lp_doc.get('challenge_price')
                    current_bid = current_bid or lp_doc.get('bid_price')
            except Exception as e:
                logger.error(f"Error fetching challenge price details: {str(e)}")

        # Prepare user details for SMS
        user_details = {
            'company_id': company_id or '',
            'company_name': company_name or 'Company'
        }

        return super().run(
            send_email=send_email,
            send_sms=send_sms,
            rfq_no=rfq_no,
            lane_no=lane_no,
            company_name=company_name,
            challenge_price=challenge_price,
            current_bid=current_bid,
            source=source,
            destination=destination,
            provider_id=provider_id,
            provider_name=provider_name,
            user_details=user_details,
            sms_template_id=sms_template_id,
            **kwargs
        )
