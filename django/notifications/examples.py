"""
Examples and test cases for the notification system.

This file demonstrates how to use the notification system in various scenarios.
"""

import logging
from .manager import NotificationManager

logger = logging.getLogger('application')


def example_create_rfq_notification():
    """Example: Send Create RFQ email notification."""
    print("=== Create RFQ Email Notification Example ===")

    manager = NotificationManager()

    # Example data
    result = manager.send_create_rfq_email(
        rfq_id='rfq_12345',
        rfq_no='RFQ001',
        company_id='company_123',
        company_name='ABC Logistics',
        rfq_title='Transportation Services Q1 2024',
        bidding_start_time='2024-01-15 10:00:00',
        bidding_end_time='2024-01-20 18:00:00',
        total_lanes=10,
        provider_ids=['provider_1', 'provider_2', 'provider_3'],
        send_email=True,
        send_sms=True
    )

    print(f"Result: {result}")
    return result


def example_send_loi_notification():
    """Example: Send LOI notification."""
    print("=== Send LOI Notification Example ===")

    manager = NotificationManager()

    result = manager.send_loi_notification(
        rfq_id='rfq_12345',
        lane_id='lane_456',
        provider_id='provider_1',
        rfq_no='RFQ001',
        lane_no='L001',
        company_id='company_123',
        company_name='ABC Logistics',
        loi_price=50000,
        source='Mumbai',
        destination='Delhi',
        provider_name='XYZ Transport',
        send_email=True,
        send_sms=False
    )

    print(f"Result: {result}")
    return result


def example_accept_loi_notification():
    """Example: Send Accept LOI notification."""
    print("=== Accept LOI Notification Example ===")

    manager = NotificationManager()

    result = manager.send_accept_loi_notification(
        rfq_id='rfq_12345',
        lane_id='lane_456',
        provider_id='provider_1',
        rfq_no='RFQ001',
        lane_no='L001',
        company_id='company_123',
        company_name='ABC Logistics',
        loi_price=50000,
        source='Mumbai',
        destination='Delhi',
        provider_name='XYZ Transport',
        send_email=True,
        send_sms=False
    )

    print(f"Result: {result}")
    return result


def example_reject_loi_notification():
    """Example: Send Reject LOI notification."""
    print("=== Reject LOI Notification Example ===")

    manager = NotificationManager()

    result = manager.send_reject_loi_notification(
        rfq_id='rfq_12345',
        lane_id='lane_456',
        provider_id='provider_1',
        rfq_no='RFQ001',
        lane_no='L001',
        company_id='company_123',
        company_name='ABC Logistics',
        loi_price=50000,
        source='Mumbai',
        destination='Delhi',
        provider_name='XYZ Transport',
        rejection_reason='Price too low',
        send_email=True,
        send_sms=False
    )

    print(f"Result: {result}")
    return result


def example_withdraw_bid_notification():
    """Example: Send Withdraw Bid notification."""
    print("=== Withdraw Bid Notification Example ===")

    manager = NotificationManager()

    result = manager.send_withdraw_bid_notification(
        rfq_id='rfq_12345',
        lane_id='lane_456',
        provider_id='provider_1',
        rfq_no='RFQ001',
        lane_no='L001',
        company_id='company_123',
        company_name='ABC Logistics',
        withdrawn_price=55000,
        source='Mumbai',
        destination='Delhi',
        provider_name='XYZ Transport',
        withdrawal_reason='Capacity constraints',
        send_email=True,
        send_sms=True
    )

    print(f"Result: {result}")
    return result


def example_challenge_price_notification():
    """Example: Send Challenge Price notification."""
    print("=== Challenge Price Notification Example ===")

    manager = NotificationManager()

    result = manager.send_challenge_price_notification(
        rfq_id='rfq_12345',
        lane_id='lane_456',
        provider_id='provider_1',
        rfq_no='RFQ001',
        lane_no='L001',
        company_id='company_123',
        company_name='ABC Logistics',
        challenge_price=45000,
        current_bid=50000,
        source='Mumbai',
        destination='Delhi',
        provider_name='XYZ Transport',
        send_email=True,
        send_sms=False
    )

    print(f"Result: {result}")
    return result


def example_using_convenience_functions():
    """Example: Using convenience functions."""
    print("=== Using Convenience Functions Example ===")

    from .manager import (
        send_create_rfq_email,
        send_loi_notification,
        send_challenge_price_notification
    )

    # Send Create RFQ notification
    result1 = send_create_rfq_email(
        rfq_id='rfq_12345',
        provider_ids=['provider_1', 'provider_2']
    )
    print(f"Create RFQ Result: {result1}")

    # Send LOI notification
    result2 = send_loi_notification(
        rfq_id='rfq_12345',
        lane_id='lane_456',
        provider_id='provider_1'
    )
    print(f"Send LOI Result: {result2}")

    # Send Challenge Price notification
    result3 = send_challenge_price_notification(
        rfq_id='rfq_12345',
        lane_id='lane_456',
        provider_id='provider_1',
        challenge_price=45000
    )
    print(f"Challenge Price Result: {result3}")


def example_integration_with_bidding_views():
    """Example: How to integrate with existing bidding views."""
    print("=== Integration with Bidding Views Example ===")

    # This is how you would integrate in your actual views

    # In CreateUpdateLOIAPI
    def send_loi_in_view(rfq_id, lane_id, provider_id):
        """Example integration in LOI creation view."""
        manager = NotificationManager()

        # Send LOI notification
        result = manager.send_loi_notification(
            rfq_id=rfq_id,
            lane_id=lane_id,
            provider_id=provider_id,
            send_email=True,
            send_sms=False
        )

        if result['email_sent']:
            logger.info(f"LOI notification sent successfully for {rfq_id}/{lane_id}/{provider_id}")
        else:
            logger.error(f"Failed to send LOI notification for {rfq_id}/{lane_id}/{provider_id}")

        return result

    # In LOIResponseAPI
    def handle_loi_response(rfq_id, lane_id, provider_id, is_accepted):
        """Example integration in LOI response view."""
        manager = NotificationManager()

        if is_accepted:
            result = manager.send_accept_loi_notification(
                rfq_id=rfq_id,
                lane_id=lane_id,
                provider_id=provider_id,
                send_email=True
            )
            action = "acceptance"
        else:
            result = manager.send_reject_loi_notification(
                rfq_id=rfq_id,
                lane_id=lane_id,
                provider_id=provider_id,
                send_email=True
            )
            action = "rejection"

        if result['email_sent']:
            logger.info(f"LOI {action} notification sent successfully")

        return result

    # Example calls
    send_loi_in_view('rfq_123', 'lane_456', 'provider_1')
    handle_loi_response('rfq_123', 'lane_456', 'provider_1', True)  # Accept
    handle_loi_response('rfq_123', 'lane_456', 'provider_2', False)  # Reject


def run_all_examples():
    """Run all examples."""
    print("Running all notification examples...\n")

    try:
        example_create_rfq_notification()
        print()

        example_send_loi_notification()
        print()

        example_accept_loi_notification()
        print()

        example_reject_loi_notification()
        print()

        example_withdraw_bid_notification()
        print()

        example_challenge_price_notification()
        print()

        example_using_convenience_functions()
        print()

        example_integration_with_bidding_views()
        print()

        print("All examples completed successfully!")

    except Exception as e:
        print(f"Error running examples: {str(e)}")
        logger.error(f"Error in notification examples: {str(e)}")


if __name__ == "__main__":
    run_all_examples()
