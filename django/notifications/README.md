# Procurement Notifications System

This notification system provides a centralized way to send email and SMS notifications for various procurement-related events.

## Overview

The notification system is based on the following table requirements:

| SL | Contracting Email Type | Mail-Transporter | Mail-Seeker | SMS Transporter |
|----|------------------------|------------------|-------------|-----------------|
| 1  | Create RFQ email       | Yes              |             | Yes             |
| 4  | Send LOI               | Yes              |             |                 |
| 5  | Accept LOI             | Yes              |             |                 |
| 6  | Reject LOI             | Yes              |             |                 |
| 10 | Withdraw bid           | Yes              | Yes         |                 |
| 13 | Challenge price        | Yes              |             |                 |

## Architecture

The system consists of:

1. **Base Notification Class** (`base.py`) - Provides common functionality for all notifications
2. **Individual Notification Classes** - Each notification type has its own class
3. **Notification Manager** (`manager.py`) - Centralized access to all notifications

## Available Notifications

### 1. Create RFQ Email (`create_rfq_email.py`)
- **Recipients**: Transporters (email + SMS)
- **Purpose**: Notify transporters when a new RFQ is created

### 2. Send LOI (`send_loi.py`)
- **Recipients**: Specific transporter (email only)
- **Purpose**: Notify transporter when LOI is sent

### 3. Accept LOI (`accept_loi.py`)
- **Recipients**: Seeker company (email only)
- **Purpose**: Notify seeker when transporter accepts LOI

### 4. Reject LOI (`reject_loi.py`)
- **Recipients**: Seeker company (email only)
- **Purpose**: Notify seeker when transporter rejects LOI

### 5. Withdraw Bid (`withdraw_bid.py`)
- **Recipients**: Seeker company (email + SMS)
- **Purpose**: Notify seeker when transporter withdraws bid

### 6. Challenge Price (`challenge_price.py`)
- **Recipients**: Specific transporter (email only)
- **Purpose**: Notify transporter when challenge price is sent

## Usage

### Using the Notification Manager

```python
from notifications import NotificationManager

# Initialize the manager
manager = NotificationManager()

# Send Create RFQ email notification
result = manager.send_create_rfq_email(
    rfq_id='rfq_123',
    rfq_no='RFQ001',
    company_name='ABC Company',
    rfq_title='Transportation Services',
    bidding_start_time='2024-01-15 10:00:00',
    bidding_end_time='2024-01-20 18:00:00',
    total_lanes=5,
    provider_ids=['provider_1', 'provider_2'],
    send_email=True,
    send_sms=True
)

# Send LOI notification
result = manager.send_loi_notification(
    rfq_id='rfq_123',
    lane_id='lane_456',
    provider_id='provider_1',
    loi_price=50000,
    send_email=True,
    send_sms=False
)
```

### Using Convenience Functions

```python
from notifications.manager import (
    send_create_rfq_email,
    send_loi_notification,
    send_accept_loi_notification,
    send_challenge_price_notification
)

# Send notifications directly
result = send_create_rfq_email(
    rfq_id='rfq_123',
    provider_ids=['provider_1', 'provider_2']
)

result = send_loi_notification(
    rfq_id='rfq_123',
    lane_id='lane_456',
    provider_id='provider_1'
)
```

### Using Individual Notification Classes

```python
from notifications.create_rfq_email import CreateRFQEmailNotification

# Create and run notification
notification = CreateRFQEmailNotification()
result = notification.run(
    rfq_id='rfq_123',
    provider_ids=['provider_1', 'provider_2'],
    send_email=True,
    send_sms=True
)
```

## Common Parameters

### Required Parameters (varies by notification type)
- `rfq_id`: RFQ identifier
- `lane_id`: Lane identifier (for lane-specific notifications)
- `provider_id`: Provider company identifier
- `company_id`: Seeker company identifier

### Optional Parameters
- `send_email`: Whether to send email (default: True)
- `send_sms`: Whether to send SMS (default: varies by notification)
- `sms_template_id`: SMS template identifier
- Various notification-specific parameters (prices, names, etc.)

## Return Value

All notification methods return a dictionary with the following structure:

```python
{
    'email_sent': bool,  # True if email was sent successfully
    'sms_sent': bool     # True if SMS was sent successfully
}
```

## Integration with Existing Code

### In Bidding Views

```python
from notifications import NotificationManager

class CreateUpdateLOIAPI(PermissionedAPIView):
    def post(self, request, *args, **kwargs):
        # ... existing LOI creation logic ...
        
        # Send LOI notification
        manager = NotificationManager()
        manager.send_loi_notification(
            rfq_id=kwargs['rfq_id'],
            lane_id=kwargs['lane_id'],
            provider_id=kwargs['provider_id'],
            send_email=True
        )
        
        # ... rest of the logic ...
```

### In RFQ Views

```python
from notifications.manager import send_create_rfq_email

class CreateDraftRFQAPI(PermissionedAPIView):
    def post(self, request, *args, **kwargs):
        # ... existing RFQ creation logic ...
        
        # Send Create RFQ notification
        send_create_rfq_email(
            rfq_id=created_rfq['id'],
            provider_ids=created_rfq['provider_ids']
        )
        
        # ... rest of the logic ...
```

## Error Handling

The notification system includes comprehensive error handling:

- Database connection errors
- Missing recipient information
- Email/SMS service failures
- Invalid parameters

All errors are logged using the Django logging system.

## Extending the System

To add a new notification type:

1. Create a new notification class inheriting from `BaseNotification`
2. Implement the required abstract methods
3. Add the notification to the `NotificationManager`
4. Add convenience functions if needed

## Dependencies

- Django email system (`utils.send_email`)
- SMS system (`utils.send_sms`)
- MongoDB utilities (`utils.mongo`)
- Logging system
