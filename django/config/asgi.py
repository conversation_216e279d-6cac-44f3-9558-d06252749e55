"""
WSGI config for accounts project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/howto/deployment/wsgi/
"""

import os
import django
from starlette.applications import Starlette
from starlette.routing import Mount
from starlette.middleware.cors import CORSMiddleware
from django.core.asgi import get_asgi_application
from django.conf import settings
from config.routes import starlette_routes

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()


# Get Django ASGI app
django_asgi_app = get_asgi_application()

# Starlette app for SSE routes
starlette_app = Starlette(routes=starlette_routes)

# Apply CORS middleware only to the Starlette app (SSE-related routes)
starlette_app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ALLOWED_ORIGINS,  # Use the Django CORS_ALLOWED_ORIGINS
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS,  # Use the Django CORS_ALLOW_CREDENTIALS
    allow_methods=['*'],  # Use the Django CORS_ALLOW_METHODS
    allow_headers=list(settings.CORS_ALLOW_HEADERS),  # Use the Django CORS_ALLOW_HEADERS
)

# Combine both apps: Starlette for SSE routes, Django for the rest
application = Starlette(
    routes=[
        Mount("/procurement/starlette-api/v1", app=starlette_app),  # Mount Starlette SSE routes
        Mount("/", app=django_asgi_app),  # Mount Django app for other routes
    ]
)
