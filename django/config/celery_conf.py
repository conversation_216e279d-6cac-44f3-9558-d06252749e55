from __future__ import absolute_import

import os
from celery import Celery
from celery.schedules import crontab, timedelta
from django.conf import settings
from utils.constants import ProcurementDBColls
from utils.mongo import MongoUtility


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

app = Celery('config')

app.config_from_object('django.conf:settings', namespace='CELERY')


def load_dynamic_schedule():
    db = MongoUtility()

    schedule_config = {}
    for doc in db.find(ProcurementDBColls.CELERY_SCHEDULES, {'enabled': True, 'module': 'procurement'}):
        if doc["schedule_type"] == "interval":
            schedule_time = timedelta(**doc["interval"])
        elif doc["schedule_type"] == "crontab":
            schedule_time = crontab(**doc["interval"])

        schedule_config[doc["task"]] = {
            "task": doc["task"],
            "schedule": schedule_time,
            "args": tuple(doc.get("args", ())),
            "kwargs": doc.get("kwargs", {})
        }

    db.client.close()
    return schedule_config


settings.CELERY_BEAT_SCHEDULE = load_dynamic_schedule()

app.autodiscover_tasks()
