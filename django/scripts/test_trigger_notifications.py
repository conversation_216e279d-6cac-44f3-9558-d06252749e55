import logging
from utils.mongo import MongoUtility
from utils.constants import ProcurementDBColls
from notifications.manager import (
    send_create_rfq_email,
    send_loi_notification
)

logger = logging.getLogger('application')


# python manage.py runscript test_trigger_notifications --script-args "rfq_id"
def run(notif_type, rfq_id, lp_id=None):
    db = MongoUtility()

    rfq_doc = db.find(ProcurementDBColls.RFQ, {'id': rfq_id}, find_one=True)
    lanes = db.find(ProcurementDBColls.LANES, {'rfq_id': rfq_id})
    total_lanes = lanes.count()

    lp_doc = db.find(ProcurementDBColls.LANE_PROVIDERS, {'id': lp_id}, find_one=True) if lp_id else {}

    if notif_type == 'create_rfq':
        notification_result = send_create_rfq_email(
            rfq_id=rfq_doc['id'],
            rfq_no=rfq_doc['rfq_no'],
            company_id=rfq_doc['company_id'],
            company_name=rfq_doc['company_name'],
            rfq_title=rfq_doc.get('rfq_title', ''),
            bidding_start_time=rfq_doc.get('bidding_start_time'),
            bidding_end_time=rfq_doc.get('bidding_end_time'),
            total_lanes=total_lanes,
            provider_ids=rfq_doc['provider_ids'],
            send_email=True,
            send_sms=False
        )
    elif notif_type == 'send_loi':
        notification_result = send_loi_notification(
            rfq_id=lp_doc['rfq_id'],
            lane_id=lp_doc['lane_id'],
            provider_id=lp_doc['provider_id'],
            rfq_no=rfq_doc.get('rfq_no'),
            company_id=rfq_doc['company_id'],
            company_name=rfq_doc['company_name'],
            loi_price=lp_doc['loi_price'],
            send_email=True,
            send_sms=False
        )

    logger.info(f'{notification_result}')
