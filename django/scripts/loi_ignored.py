import logging
from collections import defaultdict
from utils import DateUtil
from utils.mongo import MongoUtility
from utils.constants import ProcurementDBColls, LOIStatus

logger = logging.getLogger('application')
# python manage.py runscript loi_ignored


def run():
    logger.info(f'Running LOI Ignored Job !!')

    db = MongoUtility(get_new_conn=True)

    lp_ids = []
    df = {'_id': 0, 'id': 1, 'rfq_id': 1}
    now = DateUtil.get_current_timestamp()
    rfq_id_vs_loi_ignored_count_map = defaultdict(int)

    query = {
        'loi_status_id': LOIStatus.SENT.value,
        'loi_end_time': {'$lte': now}
    }

    for lp_doc in db.find(ProcurementDBColls.LANE_PROVIDERS, query, df):
        rfq_id_vs_loi_ignored_count_map[lp_doc['rfq_id']] += 1
        lp_ids.append(lp_doc['id'])

    update_query = {
        'loi_status_id': LOIStatus.IGNORED.value,
        'loi_status': LOIStatus.IGNORED.name,
        'updated_on': now
    }
    result = db.update(ProcurementDBColls.LANE_PROVIDERS, {'id': {'$in': lp_ids}}, update_query, update_many=True)

    for rfq_id, loi_ignored_count in rfq_id_vs_loi_ignored_count_map.items():
        db.update(ProcurementDBColls.RFQ, {'id': rfq_id}, inc_query={'loi_ignored': loi_ignored_count})

    message = f"LOI Ignored job completed | LOIs IGNORED: {result['modified_count']}"
    logger.info(message)

    db.client.close()
    return message
