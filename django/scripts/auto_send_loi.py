import logging
from utils import DateUtil
from utils.mongo import MongoUtility
from utils.constants import (
    ProcurementDBColls, LOIStatus,
    RFQStatus, RedisChannel,
    BiddingActions
)
from procurement.app_utils import (
    log_bulk_bid_trail,
    publish_event,
    get_product_settings,
    get_lanes_without_minimum_bidders_for_loi
)

logger = logging.getLogger('application')
# python manage.py runscript auto_send_loi --script-args rfq_id


def run(rfq_id):
    logger.info(f'Running Auto Send LOI Job !!')

    auto_loi_rank = 1

    db = MongoUtility(get_new_conn=True)

    rfq_doc = db.find(ProcurementDBColls.RFQ, {'id': rfq_id}, find_one=True)
    rfq_no = rfq_doc['rfq_no']

    try:
        if not rfq_doc:
            raise ValueError('RFQ not found.')
        elif rfq_doc['rfq_status'] != RFQStatus.COMPLETED.value:
            raise ValueError(f'[{rfq_no}] Bidding has not ended yet.')
    except ValueError as e:
        logger.error(str(e))
        return str(e)

    now = DateUtil.get_current_timestamp()
    product_settings = get_product_settings(rfq_doc['company_id'])
    min_bidders_for_loi = product_settings.get('min_bidders_for_loi')
    auto_send_loi_to_l1 = product_settings.get('auto_send_loi_to_l1')

    if not auto_send_loi_to_l1:
        message = f"[{rfq_no}] Auto LOI is disabled for the company - {rfq_doc['company_name']}."
        logger.error(message)
        return message

    invalid_lanes = get_lanes_without_minimum_bidders_for_loi(rfq_id, min_bidders_for_loi)
    if invalid_lanes:
        lane_nos = ','.join([x['lane_no'] for x in invalid_lanes])
        message = f"[{rfq_no}] Lanes-{lane_nos} : Minimum {min_bidders_for_loi} bidders required for sending LOI."
        logger.error(message)
        return message

    query = {
        'rfq_id': rfq_id,
        'loi_status_id': LOIStatus.NOT_SENT.value,
        'priority': auto_loi_rank,
    }

    df = {
        '_id': 0,
        'id': 1,
        'rfq_id': 1,
        'lane_id': 1,
        'lane_no': 1,
        'company_id': 1,
        'company_name': 1,
        'provider_id': 1,
        'provider_name': 1,
        'loi_price': 1,
    }
    lane_providers = db.find(ProcurementDBColls.LANE_PROVIDERS, query, df)

    company_ids = set({rfq_doc['company_id']})
    lp_ids, lp_docs = [], []
    for lp_doc in lane_providers:
        company_ids.add(lp_doc['provider_id'])
        lp_ids.append(lp_doc['id'])
        lp_docs.append(lp_doc)

    if not lp_ids:
        message = f'[{rfq_no}] No valid lane providers found for sending LOI.'
        logger.error(message)
        return message

    loi_start_time = now
    loi_end_time = loi_start_time + (1 * 86400000)

    update_query = {
        'loi_status_id': LOIStatus.SENT.value,
        'loi_status': LOIStatus.SENT.name,
        'loi_start_time': loi_start_time,
        'loi_end_time': loi_end_time,
        # 'loi_instructions': None,
        'loi_remarks': 'Auto triggered by system',
        'updated_on': now
    }
    result = db.update(ProcurementDBColls.LANE_PROVIDERS, {'id': {'$in': lp_ids}}, update_query, update_many=True)
    updated_lp_docs_count = result['modified_count']

    if updated_lp_docs_count:
        log_bulk_bid_trail(lp_docs, BiddingActions.LOI_SENT, 'System', now)

        db.update(ProcurementDBColls.RFQ, {'id': rfq_id}, inc_query={'loi_sent': updated_lp_docs_count})

        publish_event(RedisChannel.AUCTION_UPDATES.value, rfq_ids=[rfq_id])
        publish_event(RedisChannel.BIDDING_STATUS.value, company_ids=list(company_ids))

    message = f"[{rfq_no}] Auto send LOI job completed | LOIs Sent: {updated_lp_docs_count}"
    logger.info(message)

    db.client.close()
    return message
