import logging
from utils import DateUtil
from utils.mongo import MongoUtility
from utils.constants import (
    ProcurementDBColls, RFQStatus,
    RedisChannel
)
from procurement.app_utils import publish_event

logger = logging.getLogger('application')
# python manage.py runscript bidding_start_end


def run():
    logger.info(f'Running Bidding Job !!')

    company_ids, rfq_ids_to_start, rfq_ids_to_stop = set(), [], []
    df = {'_id': 0, 'id': 1, 'company_id': 1, 'provider_ids': 1}
    now = DateUtil.get_current_timestamp()

    db = MongoUtility(get_new_conn=True)

    # Start Bidding
    start_query = {
        'rfq_status': RFQStatus.ACTIVE.value,
        'bidding_start_time': {'$lte': now}
    }

    for rfq in db.find(ProcurementDBColls.RFQ, start_query, df):
        rfq_ids_to_start.append(rfq['id'])
        company_ids.add(rfq['company_id'])
        company_ids.update(rfq['provider_ids'])

    start_query['id'] = {'$in': rfq_ids_to_start}
    update_query = {
        'rfq_status': RFQStatus.BIDDING_IN_PROGRESS.value,
        'updated_on': now
    }
    start_result = db.update(ProcurementDBColls.RFQ, start_query, update_query, update_many=True)

    # Stop Bidding
    stop_query = {
        'rfq_status': RFQStatus.BIDDING_IN_PROGRESS.value,
        'bidding_end_time': {'$lt': now}
    }

    for rfq in db.find(ProcurementDBColls.RFQ, stop_query, df):
        rfq_ids_to_stop.append(rfq['id'])
        company_ids.add(rfq['company_id'])
        company_ids.update(rfq['provider_ids'])

    stop_query['id'] = {'$in': rfq_ids_to_stop}
    update_query = {
        'rfq_status': RFQStatus.COMPLETED.value,
        'updated_on': now
    }
    stop_result = db.update(ProcurementDBColls.RFQ, stop_query, update_query, update_many=True)

    message = f"Bidding job completed | STARTED: {start_result['modified_count']} RFQs | ENDED: {stop_result['modified_count']} RFQs"
    logger.info(message)

    publish_event(RedisChannel.BIDDING_STATUS.value, company_ids=list(company_ids))
    publish_event(RedisChannel.RFQ_STATUS_UPDATE.value, rfq_ids=rfq_ids_to_start + rfq_ids_to_stop)

    if rfq_ids_to_stop:
        from procurement.tasks import auto_send_loi
        auto_send_loi.delay(rfq_ids=rfq_ids_to_stop)

    db.client.close()
    return message
