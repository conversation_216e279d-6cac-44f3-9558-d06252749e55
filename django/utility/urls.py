from django.urls import path
from .views import (
    ListProviders,
    DropdownsAPIView,
    SearchAddress,
    CalculateTATAPI,
    GetLPPAPI,
    GetClientDetailsAPI
)

urlpatterns = [
    path('providers/listing/', ListProviders.as_view(), name='list_providers'),
    path('dropdowns/', DropdownsAPIView.as_view(), name='dropdowns'),
    path('search-address/', SearchAddress.as_view(), name='search_address'),
    path('calculate-tat/', CalculateTATAPI.as_view(), name='calculate_tat'),
    path('get-lpp/', GetLPPAPI.as_view(), name='get_lpp'),
    path('get-client-details/', GetClientDetailsAPI.as_view(), name='get_client_details'),
]
