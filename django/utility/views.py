import logging
import math
from rest_framework.views import APIView
from rest_framework import status
from django.utils.decorators import method_decorator
from utils.constants import (
    AdminDBColls,
    ProcurementDBColls,
    StopPointType,
    TatUOM,
    ApiType,
    SAASModules
)
from utils.mongo import MongoUtility
from utils import (
    format_response,
    format_error_response,
    Memoization,
    get_search_results_from_google_geocode_api,
    get_search_results_from_google_places_api,
    get_distance_with_waypoints
)
from authn import AuthenticateSeeker
from authn.decorators import cache_api_response

logger = logging.getLogger('application')


class ListProviders(APIView):
    authentication_classes = (AuthenticateSeeker, )

    def get(self, request, *args, **kwargs):
        seeker_id = request.company_id

        db = MongoUtility(module_id=SAASModules.ADMIN.value)

        query = {'seeker_id': seeker_id}
        data_filter = {
            '_id': 0,
            'id': '$provider_id',
            'name': '$provider_name'
        }
        providers = [x for x in db.find(AdminDBColls.COMPANY_MAPPING, query, data_filter, sort=[('provider_name', 1)])]

        response_data = {
            'providers': providers
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


@method_decorator(cache_api_response(timeout=86400), name='get')
class DropdownsAPIView(APIView):
    authentication_classes = (AuthenticateSeeker, )

    def get(self, request, *args, **kwargs):
        params = request.GET

        try:
            dropdown_types = params.get('dropdown_type').split(',')
        except AttributeError:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Invalid dropdown_type recieved.',
            )

        data = {}
        for dropdown_type in dropdown_types:
            if hasattr(self, f'get_{dropdown_type}_dropdown'):
                dropdown_func = getattr(self, f'get_{dropdown_type}_dropdown')
            else:
                dropdown_func = self.get_dropdown_data

            data[dropdown_type] = dropdown_func(dropdown_type)

        return format_response(status.HTTP_200_OK, data, 'Success')

    def get_dropdown_data(self, dropdown_type):
        dropdown_coll = dropdown_type
        return Memoization.get_dropdown_data(dropdown_coll)

    def get_stop_point_types_dropdown(self, dropdown_type):
        return [{'id': x.value, 'name': x.value} for x in StopPointType]

    def get_tat_uoms_dropdown(self, dropdown_type):
        return [{'id': x.value, 'name': x.value} for x in TatUOM]


class SearchAddress(APIView):
    authentication_classes = (AuthenticateSeeker, )

    def get(self, request, *args, **kwargs):
        params = request.GET
        search_term = params.get('search_term')
        lat = params.get('lat')
        lng = params.get('lng')
        place_id = params.get('place_id')
        location_type = params.get('type', 'establishment')
        api_type = int(params.get('api_type', ApiType.GOOGLE_PLACES_API.value))

        if api_type == ApiType.GOOGLE_GEOCODE_API.value:
            results = get_search_results_from_google_geocode_api(place_id, lat, lng, search_term)
        elif api_type == ApiType.GOOGLE_PLACES_API.value:
            results = get_search_results_from_google_places_api(search_term, location_type)
        else:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Invalid api_type recieved.'
            )
        return format_response(status.HTTP_200_OK, results, 'Success')


class CalculateTATAPI(APIView):
    authentication_classes = (AuthenticateSeeker, )

    def get(self, request, *args, **kwargs):
        """
        Calculate the Turn Around Time (TAT) between origin, destination, and optional waypoints.

        Query Parameters:
            - origin: Starting point (address or place_id prefixed with 'place_id:')
            - destination: Ending point (address or place_id prefixed with 'place_id:')
            - waypoints: Optional comma-separated list of waypoints
            - avg_speed: Optional average speed in km/h (default: 40)
            - hours_per_day: Optional hours of driving per day (default: 10)
            - loading_time: Optional loading time in hours (default: 3)
            - unloading_time: Optional unloading time in hours (default: 3)
            - stop_time: Optional time spent at each waypoint in hours (default: 1)

        Returns:
            - tat: Turn Around Time in days
            - tat_hours: Turn Around Time in hours
            - distance: Total distance in kilometers
            - duration: Total driving duration in hours
            - details: Additional calculation details
        """
        params = request.GET
        origin = params.get('origin')
        destination = params.get('destination')
        waypoints_param = params.get('waypoints')

        # Get optional parameters with defaults
        avg_speed = float(params.get('avg_speed', 20))  # km/h
        hours_per_day = float(params.get('hours_per_day', 15))  # hours
        loading_time = float(params.get('loading_time', 0))  # hours
        unloading_time = float(params.get('unloading_time', 0))  # hours
        stop_time = float(params.get('stop_time', 0))  # hours per waypoint

        if not origin or not destination:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Both origin and destination are required.'
            )

        waypoints = waypoints_param.split(',') if waypoints_param else None
        num_waypoints = len(waypoints) if waypoints else 0

        # Calculate distance using Google Directions API
        result = get_distance_with_waypoints(origin, destination, waypoints)

        if not result['is_success']:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                result['error_message']
            )

        # Calculate TAT
        distance_km = result['total_distance'] / 1000  # Convert meters to kilometers

        # Calculate driving time based on average speed
        driving_hours = distance_km / avg_speed

        # Add loading, unloading, and stop times
        total_hours = driving_hours + loading_time + unloading_time + (num_waypoints * stop_time)

        # Convert to days (rounded up to the nearest day)
        tat_days = math.ceil(total_hours / hours_per_day)

        response_data = {
            'tat': tat_days,
            'tat_uom': TatUOM.DAYS.value,
            'tat_hours': total_hours,
            'distance_km': int(round(distance_km, 0)),
            'driving_hours': round(driving_hours, 2),
            'details': {
                'loading_time': loading_time,
                'unloading_time': unloading_time,
                'waypoints_time': num_waypoints * stop_time,
                'avg_speed': avg_speed,
                'hours_per_day': hours_per_day,
                'num_waypoints': num_waypoints,
                'route_info': result
            }
        }

        return format_response(status.HTTP_200_OK, response_data, 'TAT calculated successfully')


class GetLPPAPI(APIView):
    authentication_classes = (AuthenticateSeeker, )

    def get(self, request, *args, **kwargs):
        from procurement.app_utils import generate_lpp_id

        params = request.GET

        rfq = {
            'pricing_basis': params.get('pricing_basis'),
        }
        lane = {
            'company_id': request.company_id,
            'src_city': params.get('src_city'),
            'dst_city': params.get('dst_city'),
            'vehicle_type': params.get('vehicle_type'),
            'vehicle_capacity': params.get('vehicle_capacity'),
            'body_type': params.get('body_type'),
        }
        lpp_id = generate_lpp_id(rfq, lane)

        db = MongoUtility()

        query = {'id': lpp_id, 'company_id': request.company_id}

        doc = db.find(ProcurementDBColls.LPP, query, find_one=True)

        response_data = {
            'lpp_id': lpp_id,
            'lpp': doc.get('lpp'),
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class GetClientDetailsAPI(APIView):

    def get(self, request, *args, **kwargs):
        from utils import get_client_details
        response_data = {
            'client_details': get_client_details(request)
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')
