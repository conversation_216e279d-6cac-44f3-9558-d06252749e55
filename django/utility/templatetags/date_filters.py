from django import template
from utils.date_util import DateUtil

register = template.Library()


@register.filter
def format_timestamp_email(value):
    """
    Django template filter to format epoch timestamps for email templates.
    Converts epoch timestamp to IST and formats as "9th April 2025, 09:30 A.M"
    
    Usage in templates:
    {{ message.bidding_start_time|format_timestamp_email }}
    """
    return DateUtil.format_timestamp_for_email(value)


@register.filter
def format_timestamp_custom(value, format_string=None):
    """
    Django template filter to format epoch timestamps with custom format.
    
    Usage in templates:
    {{ message.bidding_start_time|format_timestamp_custom:"%d/%m/%Y %H:%M" }}
    """
    if not format_string:
        format_string = '%Y-%m-%dT%H:%M'
    
    return DateUtil.format_timestamp(value, format_string)
