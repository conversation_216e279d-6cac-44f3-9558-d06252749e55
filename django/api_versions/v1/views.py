from rest_framework import status
from rest_framework.views import APIView
from authn import AuthenticateSuperAdmin
from utils import (
    Memoization,
    format_response,
)


class ClearCache(APIView):
    authentication_classes = (AuthenticateSuperAdmin, )

    def get(self, request):
        cached_data = Memoization.__dict__
        for key in cached_data:
            if isinstance(cached_data[key], dict):
                setattr(Memoization, key, {})
            elif isinstance(cached_data[key], list):
                setattr(Memoization, key, [])

        return format_response(status.HTTP_200_OK, {}, 'Success')
