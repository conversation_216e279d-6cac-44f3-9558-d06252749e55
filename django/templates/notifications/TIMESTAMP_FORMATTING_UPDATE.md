# Timestamp Formatting Update for Email Templates

## Overview

This document describes the implementation of timestamp formatting for notification email templates. Previously, epoch timestamp values were being displayed directly in templates. Now they are converted to IST timezone and formatted in a user-friendly format.

## Changes Made

### 1. Enhanced DateUtil Class

**File**: `django/utils/date_util.py`

Added a new method `format_timestamp_for_email()` that:
- Converts epoch timestamps to IST timezone
- Formats them as "9th April 2025, 09:30 AM"
- Handles edge cases (None, 0, invalid values) by returning "TBD"

```python
@staticmethod
def format_timestamp_for_email(value):
    """
    Format timestamp for email templates in the format: "9th April 2025, 09:30 A.M"
    """
    if not value:
        return 'TBD'
    
    try:
        # Convert epoch timestamp to IST datetime
        datetime_obj = DateUtil.utc_to_ist_datetime(value)
        
        # Get day with suffix (1st, 2nd, 3rd, 4th, etc.)
        day = datetime_obj.day
        suffix = DateUtil.date_suffix(day)
        
        # Format the datetime
        formatted_date = datetime_obj.strftime(f"{day}{suffix} %B %Y, %I:%M %p")
        
        return formatted_date
        
    except Exception:
        return 'TBD'
```

### 2. Created Django Template Filter

**File**: `django/utility/templatetags/date_filters.py` (New)

Created a Django template filter that wraps the DateUtil function:

```python
from django import template
from utils.date_util import DateUtil

register = template.Library()

@register.filter
def format_timestamp_email(value):
    """
    Django template filter to format epoch timestamps for email templates.
    Converts epoch timestamp to IST and formats as "9th April 2025, 09:30 A.M"
    
    Usage in templates:
    {{ message.bidding_start_time|format_timestamp_email }}
    """
    return DateUtil.format_timestamp_for_email(value)
```

**File**: `django/utility/templatetags/__init__.py` (New)

Created the templatetags package initialization file.

### 3. Updated Email Templates

**File**: `django/templates/notifications/create_rfq_email.html`

Updated the template to:
1. Load the date_filters template tag library
2. Apply the `format_timestamp_email` filter to timestamp fields

```django
{% extends "base.html" %}
{% load date_filters %}

<!-- ... -->

<td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #212529; font-family: Verdana, arial, Helvetica, sans-serif;">
    {{ message.bidding_start_time|format_timestamp_email }}
</td>

<!-- ... -->

<td style="padding: 8px 0; color: #dc3545; font-family: Verdana, arial, Helvetica, sans-serif;">
    <strong>{{ message.bidding_end_time|format_timestamp_email }}</strong>
</td>
```

### 4. Updated Documentation

**File**: `django/templates/notifications/DJANGO_TEMPLATE_INTEGRATION.md`

Updated the documentation to include:
- The new `format_timestamp_email` template filter
- Examples of timestamp formatting usage

## Format Examples

The new timestamp formatting converts epoch values to the following format:

| Input (Epoch) | Output (IST) |
|---------------|--------------|
| 1735545000000 | 30th December 2024, 06:50 PM |
| 1743465000000 | 1st April 2025, 10:50 AM |
| 1733145000000 | 3rd December 2024, 12:10 AM |
| None or 0 | TBD |

## Benefits

1. **User-Friendly**: Timestamps are now displayed in a readable format
2. **Timezone Aware**: All timestamps are converted to IST
3. **Consistent**: All email templates use the same formatting
4. **Robust**: Handles edge cases gracefully
5. **Maintainable**: Centralized formatting logic in DateUtil

## Testing

The implementation has been thoroughly tested:
- ✅ Timestamp conversion accuracy
- ✅ IST timezone conversion
- ✅ Format consistency
- ✅ Edge case handling (None, 0, invalid values)
- ✅ Template integration
- ✅ Django filter registration

## Usage in Future Templates

To use timestamp formatting in new email templates:

1. Load the date_filters template tag:
   ```django
   {% load date_filters %}
   ```

2. Apply the filter to timestamp variables:
   ```django
   {{ message.your_timestamp_field|format_timestamp_email }}
   ```

## Backward Compatibility

This change is backward compatible:
- Existing templates without the filter will continue to work
- The DateUtil class maintains all existing functionality
- No changes required to notification sending logic

## Files Modified

1. `django/utils/date_util.py` - Added `format_timestamp_for_email()` method
2. `django/utility/templatetags/__init__.py` - New file
3. `django/utility/templatetags/date_filters.py` - New file
4. `django/templates/notifications/create_rfq_email.html` - Updated to use new filter
5. `django/templates/notifications/DJANGO_TEMPLATE_INTEGRATION.md` - Updated documentation
6. `django/notifications/test_templates.py` - Updated test data to use epoch timestamps
