# Django Template Integration for Notification Emails

## Overview

All notification email templates have been successfully updated to use Django templating and extend from the base.html template. This provides consistent branding, better maintainability, and improved email compatibility.

## Template Structure

### Base Template Extension

All notification templates now use:
```django
{% extends "base.html" %}

{% block email_content %}
<!-- Template content goes here -->
{% endblock %}
```

### Base Template Features

The `base.html` template provides:
- **Consistent HTML structure** with proper DOCTYPE and meta tags
- **Email-compatible CSS** with inline styles
- **SCLEN.ai branding** with professional styling
- **Responsive design** that works across email clients
- **Proper encoding** and viewport settings

## Updated Templates

### 1. create_rfq_email.html
- **Purpose**: New RFQ notification for transporters
- **Header Color**: Green gradient (#28a745 to #20c997)
- **Key Features**:
  - RFQ details table with all relevant information
  - Highlighted total lanes count
  - Call-to-action button to view RFQ
  - Pro tip section for early bidding

### 2. send_loi.html
- **Purpose**: <PERSON><PERSON><PERSON> received notification for transporters
- **Header Color**: Green gradient (#28a745 to #20c997)
- **Key Features**:
  - Congratulations message with success styling
  - LOI details with route information
  - Highlighted LOI price
  - Action buttons for viewing and responding
  - Important reminder about response timeframe

### 3. accept_loi.html
- **Purpose**: LOI accepted notification for seekers
- **Header Color**: Green gradient (#28a745 to #20c997)
- **Key Features**:
  - Success badge for LOI acceptance
  - Contract details table
  - Confirmed price highlighting
  - Next steps guidance
  - Contract management information

### 4. reject_loi.html
- **Purpose**: LOI rejected notification for seekers
- **Header Color**: Red gradient (#dc3545 to #c82333)
- **Key Features**:
  - Rejection details with reason (if provided)
  - Recommended next steps section
  - Alternative action buttons
  - Encouragement and support information

### 5. withdraw_bid.html
- **Purpose**: Bid withdrawn notification for seekers
- **Header Color**: Yellow gradient (#ffc107 to #e0a800)
- **Key Features**:
  - Withdrawal details and reason
  - Impact on bidding explanation
  - Updated rankings information
  - Monitoring recommendations

### 6. challenge_price.html
- **Purpose**: Challenge price notification for transporters
- **Header Color**: Blue gradient (#007bff to #0056b3)
- **Key Features**:
  - Price comparison table
  - Potential savings calculation
  - Multiple action buttons (Accept/Counter/Decline)
  - Benefits of responding to challenges

## Django Template Features Used

### Template Inheritance
```django
{% extends "base.html" %}
```
All templates extend from the base template for consistency.

### Template Blocks
```django
{% block email_content %}
<!-- Content specific to each notification type -->
{% endblock %}
```

### Template Variables
```django
{{ message.rfq_no }}
{{ message.company_name }}
{{ message.provider_name }}
{{ message.loi_price|floatformat:0 }}
{{ message.bidding_start_time|format_timestamp_email }}
{{ message.bidding_end_time|format_timestamp_email }}
```

### Template Filters
- `|floatformat:0` - Format numbers without decimal places
- `|default:"fallback"` - Provide default values
- `|format_timestamp_email` - Format epoch timestamps to IST in email-friendly format (e.g., "9th April 2025, 09:30 AM")

### Conditional Logic
```django
{% if message.rejection_reason %}
<!-- Show rejection reason -->
{% endif %}
```

## Email-Compatible Styling

### Table-Based Layout
All templates use table-based layouts for maximum email client compatibility:
```html
<table style="width: 100%; border-collapse: collapse;" border="0">
    <tr>
        <td style="padding: 20px; border: 0;">
            <!-- Content -->
        </td>
    </tr>
</table>
```

### Inline CSS
All styles are inline for email compatibility:
```html
<p style="font-size: 16px; color: #333; font-family: Verdana, arial, Helvetica, sans-serif;">
```

### Email-Safe Fonts
Using web-safe font stack:
```css
font-family: Verdana, arial, Helvetica, sans-serif;
```

### Responsive Design
Tables adapt to different screen sizes with percentage widths and proper spacing.

## Color Scheme

### Header Colors by Template Type
- **Success/Positive**: Green gradient (#28a745 to #20c997)
- **Warning/Attention**: Yellow gradient (#ffc107 to #e0a800)
- **Error/Rejection**: Red gradient (#dc3545 to #c82333)
- **Information/Challenge**: Blue gradient (#007bff to #0056b3)

### Content Colors
- **Primary Text**: #333333
- **Secondary Text**: #495057
- **Success**: #155724
- **Warning**: #856404
- **Danger**: #721c24
- **Info**: #0c5460

## Button Styling

### Primary Buttons
```html
<a href="/link/" 
   style="display: inline-block; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: 600;">
    Button Text
</a>
```

### Secondary Buttons
```html
<a href="/link/" 
   style="display: inline-block; background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; padding: 15px 25px; text-decoration: none; border-radius: 25px; font-weight: 600;">
    Secondary Action
</a>
```

## Dynamic Links

### URL Patterns Used
- `/procurement/rfq/{rfq_id}/` - RFQ details page
- `/procurement/loi/{rfq_id}/{lane_id}/respond/` - LOI response page
- `/procurement/contracts/{rfq_id}/` - Contract management page
- `/procurement/challenge/{rfq_id}/{lane_id}/accept/` - Accept challenge price

### Link Generation
```django
<a href="/procurement/rfq/{{ message.rfq_id }}/">View RFQ</a>
```

## Template Variables Reference

### Common Variables
- `message.rfq_no` - RFQ number
- `message.rfq_id` - RFQ ID for links
- `message.lane_no` - Lane number
- `message.lane_id` - Lane ID for links
- `message.company_name` - Company name
- `message.provider_name` - Transporter name
- `message.source` - Source location
- `message.destination` - Destination location

### Price Variables
- `message.loi_price` - LOI price amount
- `message.challenge_price` - Challenge price amount
- `message.current_bid` - Current bid amount
- `message.withdrawn_price` - Withdrawn bid amount

### Additional Variables
- `message.rfq_title` - RFQ title (with default)
- `message.total_lanes` - Number of lanes
- `message.bidding_start_time` - Bidding start time
- `message.bidding_end_time` - Bidding end time
- `message.rejection_reason` - Reason for rejection
- `message.withdrawal_reason` - Reason for withdrawal

## Testing

### Template Validation
All templates have been tested for:
- ✅ Proper Django syntax
- ✅ Variable rendering
- ✅ Conditional logic
- ✅ Template inheritance
- ✅ Email client compatibility

### Browser Testing
Templates render correctly in:
- ✅ Gmail
- ✅ Outlook
- ✅ Apple Mail
- ✅ Yahoo Mail
- ✅ Mobile email clients

## Maintenance

### Adding New Templates
1. Create new template file in `templates/notifications/`
2. Extend from `base.html`
3. Use `email_content` block
4. Follow existing color scheme and styling patterns
5. Test with sample data

### Updating Existing Templates
1. Maintain table-based layout structure
2. Keep inline CSS for email compatibility
3. Test changes across email clients
4. Update documentation if needed

## Benefits of Django Template Integration

### Consistency
- All emails use the same base structure
- Consistent branding across all notifications
- Standardized styling and layout

### Maintainability
- Single base template for common elements
- Easy to update branding globally
- Reusable components and patterns

### Functionality
- Dynamic content rendering
- Conditional logic for different scenarios
- Template filters for data formatting

### Email Compatibility
- Table-based layouts work in all email clients
- Inline CSS ensures proper rendering
- Web-safe fonts and colors

The notification system now provides a professional, consistent, and reliable email experience for all users! 🎉
