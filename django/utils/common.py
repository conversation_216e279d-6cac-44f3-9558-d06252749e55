import os
import re
import time
import json
import logging
import requests
import traceback
from uuid import uuid4
from urllib import parse
from pypdf import PdfReader
from pypdf.errors import PdfReadError
from PIL import Image, UnidentifiedImageError
from starlette.requests import Request as StarletteRequest
from django.conf import settings
from .mongo import MongoUtility
from .constants import (
    AdminDBColls, FileTypes, ImageTypes,
    FILE_TYPE_BACKEND, FILE_TYPE_HELPERS
)
from .date_util import DateUtil


logger = logging.getLogger('application')


def get_uuid():
    return str(uuid4()).replace('-', '')


def get_domain_from_url(url):
    parsed_url = parse.urlsplit(url)
    return parsed_url.netloc


def get_parsed_url_data(url):
    data = {}
    parsed_obj = parse.urlsplit(url)
    scheme, host = parsed_obj.scheme, parsed_obj.netloc
    if scheme and host:
        data.update({
            'origin': '{}://{}'.format(scheme, host),
            'scheme': scheme,
            'host': host,
            'path': parsed_obj.path,
        })
    return data


def get_client_details(request):
    if isinstance(request, StarletteRequest):
        headers = dict(request.headers)
        client_host, _ = request.client or (None, None)
        details = {
            'http_x_forwarded_for': headers.get('x-forwarded-for'),
            'remote_addr': client_host,
            'http_host': headers.get('host'),
            'request_method': request.method,
            'path_info': request.url.path,
            'server_protocol': request.scope.get('http_version'),
            'content_type': headers.get('content-type'),
            'http_user_agent': headers.get('user-agent'),
        }
    else:
        meta_data = request.META
        meta_keys = [
            'HTTP_X_FORWARDED_FOR',
            'REMOTE_ADDR',
            'HTTP_HOST',
            'REQUEST_METHOD',
            'PATH_INFO',
            'SERVER_PROTOCOL',
            'CONTENT_TYPE',
            'HTTP_USER_AGENT',
        ]
        details = {key.lower(): meta_data.get(key) for key in meta_keys}

    if details['http_x_forwarded_for']:
        details['ip'] = details['http_x_forwarded_for'].split(',')[0]
    else:
        details['ip'] = details['remote_addr']
    return details


def get_traceback(e):
    try:
        return ''.join(traceback.TracebackException.from_exception(e).format())
    except Exception as err:
        return 'Error occurred while fetching traceback of original error ({}). {}'.format(e, err)


def fetch_response(request_url, params=None, headers=None, method='GET', payload='', timeout=30):
    if headers and ('cache-control' not in headers):
        headers.update({'cache-control': 'no-cache'})
    return requests.request(method, request_url, data=payload, headers=headers, params=params, timeout=timeout)


def execute_task_in_thread(target, log_msg, args=(), **kwargs):
    from threading import Thread
    logger.info(log_msg)

    delay = int(kwargs.get('delay') or 0)
    if delay:
        time.sleep(delay)

    thread_inst = Thread(target=target, args=args)
    thread_inst.start()
    # logger.info(f'After sap trigger.. {thread_inst.is_alive()}')
    return thread_inst


def log_api_request(api_type, request, company_id=None, logs_coll=AdminDBColls.API_REQUEST_LOGS, **kwargs):
    try:
        db = MongoUtility()

        try:
            request_method = request.method
        except AttributeError:
            request_method = 'GET'

        request_body = None
        if request_method.lower() == 'post':
            try:
                request_body = kwargs.get('request_payload', {})
                if isinstance(request_body, str):
                    try:
                        request_body = json.loads(request_body)
                    except ValueError:
                        pass
            except (AttributeError, ValueError):
                request_body = {}

        try:
            url = request.get_raw_uri()
        except AttributeError:
            url = None  # noqa

        obj = {
            'api_type': api_type.value,
            'company_id': company_id,
            'api_name': api_type.name,
            'request_url': url,
            'request_method': request_method,
            'request_body': request_body,
            # 'request_headers': headers,
            # 'response_status_code': 200,
            # 'response_body': raw_response,
            # 'response_type': response_type,
            'created_on': DateUtil.get_current_timestamp(),
            'datetime': DateUtil.get_current_timestamp(True),
            **kwargs
        }
        db.insert(logs_coll, [obj])
    except Exception as e:
        logger.error('[Unhandled Exception] {}'.format(get_traceback(e)))
        return False
    return True


def is_file_corrupt(file, file_type):
    if file_type == FileTypes.PDF.value:
        try:
            reader = PdfReader(file)
            _ = len(reader.pages)  # Attempt to read the number of pages
            return False  # File is not corrupt
        except (PdfReadError, Exception) as e:
            logger.error(f'[PDF_FILE_ERROR] {e}')
    elif file_type in [x.value for x in ImageTypes]:
        try:
            img = Image.open(file)
            img.verify()  # Verify that it is an image
            return False  # File is not corrupt
        except (UnidentifiedImageError, Exception) as e:
            logger.error(f'[IMAGE_FILE_ERROR] {e}')
    return False


def validate_file_type_and_size(uploaded_file, allowed_types=None, max_file_size=settings.MAX_FILE_SIZE):
    import magic
    filetype = magic.from_buffer(uploaded_file.file.read(), mime=True).lower()

    if not allowed_types:
        allowed_types = [x.value for x in FileTypes]

    allowed_types_backend = [FILE_TYPE_BACKEND[x] for x in allowed_types if x in FILE_TYPE_BACKEND]

    errors = []
    valid_type = False
    for typ in allowed_types + allowed_types_backend:
        if filetype in typ:
            valid_type = True
            break

        additional_type = FILE_TYPE_HELPERS.get(typ)
        if additional_type is not None and filetype in additional_type:
            valid_type = True
            break

    file_name = uploaded_file.name
    file_ext = file_name.split(".")[-1].lower()
    if filetype not in FILE_TYPE_HELPERS.get(file_ext, []):
        valid_type = False

    if not valid_type:
        errors.append("Invalid file type ({}: {}). Allowed file types are: {}".format(file_name, filetype, allowed_types))

    if uploaded_file.size > max_file_size:
        errors.append("File too big ({}: {}). Allowed max file size is: {}".format(file_name, uploaded_file.size, max_file_size))

    if is_file_corrupt(uploaded_file, file_ext):
        errors.append(f"Corrupt/Unsupported file - {file_name}")

    is_file_valid = not bool(errors)
    return is_file_valid, errors


def clean_filename(filename, replacement_char='_', max_length=100, add_timestamp=False):
    # Define a pattern for invalid characters (anything other than letters, digits, hyphens, underscores, and dots)
    invalid_chars_pattern = r'[^\w\-.]'

    # Split the filename into name and extension
    name, ext = os.path.splitext(filename)

    # Remove leading and trailing whitespace
    name = name.strip()

    # Replace invalid characters
    name = re.sub(invalid_chars_pattern, replacement_char, name)

    # Collapse multiple consecutive dots into a single dot
    name = re.sub(r'\.{2,}', '.', name)

    # Truncate the filename if it exceeds the max length
    if len(name) > max_length:
        name = name[:max_length]

    if add_timestamp:
        now = DateUtil.format_timestamp(DateUtil.get_current_timestamp(), '%Y%m%dT%H%M%S')
        name = f'{name}_{now}'

    # Combine the cleaned name with the original extension
    cleaned_filename = f"{name}{ext}"

    # Ensure the final filename does not start or end with a dot or replacement character
    cleaned_filename = cleaned_filename.strip('.').strip(replacement_char)
    return cleaned_filename
