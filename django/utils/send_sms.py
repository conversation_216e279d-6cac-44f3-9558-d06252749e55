import json
import logging
from requests.exceptions import RequestException
from utils.constants import (
    SMSVendor,
    AdminDBColls,
    ErrorMessages
)
from utils.mongo import MongoUtility
from .common import fetch_response
from .date_util import DateUtil
from .memoization import Memoization

logger = logging.getLogger('application')


def send_sms(sms_details={}):
    receivers = sms_details['receivers']
    if not receivers:
        return False

    db = MongoUtility()

    company_id = sms_details['user_details']['company_id']
    company_name = sms_details['user_details']['company_name']
    message = sms_details['message']

    template_id = sms_details['template_id']
    new_template_ids = sms_details['new_template_ids']
    alert_type = sms_details['alert_type']
    message_params = sms_details['message_params']

    sms_vendor = Memoization.get_sms_vendor_config(SMSVendor.BOSCHINDIA)
    platform = sms_vendor["name"]

    api = sms_vendor["config"]["api"]
    headers = sms_vendor["config"]["headers"]
    message_suffix = sms_vendor["config"]["message_suffix"]
    payload = sms_vendor["config"]["payload"]
    method = sms_vendor["config"]["method"]

    message += message_suffix
    receivers = list(set([str(x) for x in receivers]))
    receivers_list = [{"phoneNumber": '+91' + x} if len(x) != 12 else {"phoneNumber": "+" + x} for x in receivers]

    payload.update({
        "channel": template_id,
        'message': message,
        'receivers': receivers_list,
        'commonMessageParams': message_params,
    })
    payload = json.dumps(payload)

    try:
        response = fetch_response(api, {}, headers, method, payload=payload)
        status_code = response.status_code
    except RequestException as e:
        logger.error('{} {}'.format(ErrorMessages.SMS_ALERT, str(e)))

    try:
        response_data = response.json()
    except AttributeError:
        response_data = {}

    try:
        response_text = response.text.lower()
    except AttributeError:
        response_text = ''

    logger.info('[{}] Message: {}, Response: {}'.format(template_id, message, response_data))
    total_sent = 0
    try:
        is_response_success = response_data['status'] == 'success'
        if is_response_success:
            total_sent = len([x['to'] for x in response_data['recipients'] if x['submission'] == 'success'])
    except KeyError:
        is_response_success = False
        if template_id in new_template_ids and status_code == 202:
            is_response_success = True
            total_sent = response_data["totalSmsCount"]

    response_data.update({
        "created": DateUtil.get_current_timestamp(),
        "datetime": DateUtil.get_current_timestamp(True),
        "company_id": company_id,
        "company_name": company_name,
        "receivers": receivers,
        "number_of_recipient": len(receivers),
        "platform": platform,
        "message": message,
        "requestTemplateName": alert_type,
        "message_params": message_params,
        "receivers_list": receivers_list,
        "status_code": status_code
    })

    db.insert(AdminDBColls.SMS_SENT_LOGS, [response_data])

    if status_code not in [200, 202] or (not is_response_success) or total_sent <= 0:
        error = 'Failed to send sms to {} ({})'.format(receivers, response_text)
        logger.error('{} {}'.format(ErrorMessages.SMS_ALERT, error))
    else:
        logger.info("[SMS_LOGS] SMS sent successfully to {} - {}".format(receivers, message))
        return True
    return False
