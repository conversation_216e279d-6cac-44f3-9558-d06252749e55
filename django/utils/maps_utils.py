import logging
from utils import Memoization, fetch_response
from utils.constants import ApiType

logger = logging.getLogger('application')


def get_search_results_from_google_geocode_api(place_id=None, lat=None, lng=None, address=None):
    api_config = Memoization.get_api_config(ApiType.GOOGLE_GEOCODE_API)
    api_key = Memoization.get_api_key()

    params = {
        "key": api_key,
        "language": "en"  # Force results in English, but not 100% effective
    }
    if place_id:
        params["place_id"] = place_id
    elif (lat and lng):
        params["latlng"] = f'{lat},{lng}'
    elif address:
        params["address"] = address
        params["components"] = "country:IN"  # Restrict to India

    response = fetch_response(api_config['api'], params, headers=api_config['headers'], method=api_config['method'])
    data = response.json()

    status_code = data.get("status", "Unknown status")
    error_message = data.get("error_message", "No results found") if status_code != "OK" else None
    results = []

    for result in data.get("results", []):
        address_components = result.get("address_components", [])

        city = state = state_code = pincode = None
        street_address = result.get("formatted_address", "")

        for component in address_components:
            types = component.get("types", [])

            if "locality" in types:
                city = component.get("long_name")
            elif "administrative_area_level_1" in types:
                state = component.get("long_name")
                state_code = component.get("short_name")
            elif "postal_code" in types:
                pincode = component.get("long_name")

        results.append({
            "address": street_address,
            "pincode": int(pincode) if pincode and pincode.isdigit() else None,
            "city": city,
            "state": state,
            "state_code": state_code,
        })

    return {
        "status": status_code,
        "error_message": error_message,
        "count": len(results),
        "results": results,
        'is_success': bool(results)
    }


def get_search_results_from_google_places_api(search_term, location_type='establishment'):
    api_config = Memoization.get_api_config(ApiType.GOOGLE_PLACES_API)
    api_key = Memoization.get_api_key()

    params = {
        "query": search_term,
        "region": "IN",  # Prioritize India
        "key": api_key,
        "language": "en"  # Force results in English, but not 100% effective
    }
    if location_type:
        # type=establishment parameter works as a bias—it influences the ranking of results
        # but doesn't guarantee that only establishments will be returned
        params['type'] = location_type

    response = fetch_response(api_config['api'], params, headers=api_config['headers'], method=api_config['method'])
    data = response.json()

    status_code = data.get("status", "Unknown status")
    error_message = data.get("error_message", "No results found") if status_code != "OK" else None
    results = []

    for result in data.get("results", []):
        location = result.get("geometry", {}).get("location", {})

        results.append({
            "place_name": result.get("name"),  # Place name
            "address": result.get("formatted_address"),  # Full address
            "lat": location.get("lat"),
            "lng": location.get("lng"),
            "place_id": result.get("place_id"),  # Google Place ID
        })

    return {
        "status": status_code,
        "error_message": error_message,
        "count": len(results),
        "results": results,
        'is_success': bool(results)
    }


def get_distance_with_waypoints(origin, destination, waypoints=None):
    """
    Calculate the total distance between an origin, destination, and optional waypoints using Google Directions API.

    Args:
        origin (str): The starting point. Can be a place_id (prefixed with 'place_id:') or an address string.
        destination (str): The ending point. Can be a place_id (prefixed with 'place_id:') or an address string.
        waypoints (str, optional): A str of '|' separated intermediate points. Each can be a place_id (prefixed with 'place_id:') or an address string.

    Returns:
        dict: A dictionary containing:
            - status (str): The status of the API request (e.g., 'OK', 'ZERO_RESULTS', etc.)
            - error_message (str): Error message if the request failed
            - total_distance (int): Total distance in meters
            - total_duration (int): Total duration in seconds
            - legs (list): Details of each leg of the journey
            - is_success (bool): Whether the request was successful
    """
    api_config = Memoization.get_api_config(ApiType.GOOGLE_DIRECTIONS_API)
    api_key = Memoization.get_api_key()

    params = {
        "key": api_key,
        "origin": origin,
        "destination": destination,
        "language": "en",
        "units": "metric",  # Use metric units (kilometers)
    }

    # Add waypoints if provided
    if waypoints:
        # Format waypoints as a pipe-separated string
        params["waypoints"] = waypoints

    response = fetch_response(api_config['api'], params, headers=api_config['headers'], method=api_config['method'])
    data = response.json()

    status_code = data.get("status", "Unknown status")
    is_success = status_code == "OK"
    error_message = data.get("error_message", "No results found") if not is_success else None

    if not (is_success or data.get("routes")):
        return {
            "status": status_code,
            "error_message": error_message or "No routes found",
            "total_distance": 0,
            "total_duration": 0,
            "legs": [],
            "is_success": False
        }

    # Get the first route (Google may provide alternatives)
    route = data["routes"][0]

    # Calculate total distance and duration
    total_distance = 0
    total_duration = 0
    legs = []

    for leg in route["legs"]:
        distance = leg.get("distance", {}).get("value", 0)  # Distance in meters
        duration = leg.get("duration", {}).get("value", 0)  # Duration in seconds

        total_distance += distance
        total_duration += duration

        legs.append({
            "start_address": leg.get("start_address"),
            "end_address": leg.get("end_address"),
            "distance": {
                "text": leg.get("distance", {}).get("text", ""),
                "value": distance
            },
            "duration": {
                "text": leg.get("duration", {}).get("text", ""),
                "value": duration
            }
        })

    return {
        "status": status_code,
        "error_message": error_message,
        "total_distance": total_distance,
        "total_duration": total_duration,
        "legs": legs,
        "is_success": is_success
    }
