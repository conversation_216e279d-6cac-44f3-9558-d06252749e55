from datetime import datetime
from dateutil.parser import parse
from utils.constants import IST_TZ


class DateUtil(object):

    @staticmethod
    def convert_to_unix(timestamp):
        if timestamp:
            if isinstance(timestamp, str):
                timestamp = parse(timestamp)
            return int(timestamp.strftime('%s')) * 1000

    @staticmethod
    def format_to_datetime(datetime_string, string_format):
        if datetime_string:
            return datetime.strptime(datetime_string, string_format)

    @staticmethod
    def convert_to_datetime(timestamp, string_format='', tz=None):
        if timestamp:
            if isinstance(timestamp, str):
                if string_format:
                    timestamp = datetime.strptime(timestamp, string_format)
                else:
                    timestamp = parse(timestamp)
            else:
                try:
                    timestamp = datetime.fromtimestamp(int(timestamp))
                except ValueError:
                    timestamp = datetime.fromtimestamp(int(timestamp) / 1000)

            if tz:
                return tz.localize(timestamp.replace(tzinfo=None))
            return timestamp.replace(tzinfo=None)

    @staticmethod
    def get_current_datetime(tz=IST_TZ):
        # get datetime object of the given timezone
        return datetime.now(tz=tz)

    @staticmethod
    def get_current_timestamp(get_date_obj=False):
        # timestamps are always unix epoch values which are always UTC.
        utc_time = datetime.utcnow()
        if get_date_obj:
            return utc_time
        return int(utc_time.timestamp() * 1000)

    @staticmethod
    def convert_from_utc(timestamp, to_tz=IST_TZ):
        """Method expects a datetime object or a valid datetime string(Ex: '2023-03-26 00:00:00') or an integer epoch and returns a timestamp in ms."""
        if timestamp:
            if isinstance(timestamp, str):
                timestamp = parse(timestamp)

            if isinstance(timestamp, datetime):
                timestamp = timestamp.timestamp() * 1000

            # Convert to given time zone
            delta_milli_sec = to_tz.utcoffset(datetime.now()).total_seconds() * 1000
            return int(timestamp + delta_milli_sec)

    @staticmethod
    def convert_to_utc(timestamp, from_tz=IST_TZ):
        """Method expects a datetime object or a valid datetime string(Ex: '2023-03-26 00:00:00') or an integer epoch and returns utc timestamp in ms."""
        if timestamp:
            if isinstance(timestamp, str):
                timestamp = parse(timestamp)

            if isinstance(timestamp, datetime):
                timestamp = timestamp.timestamp() * 1000

            # Convert from given time zone
            delta_milli_sec = from_tz.utcoffset(datetime.now()).total_seconds() * 1000
            return int(timestamp - delta_milli_sec)

    @staticmethod
    def utc_to_ist_datetime(timestamp):
        return DateUtil.convert_to_datetime(DateUtil.convert_from_utc(timestamp))

    @staticmethod
    def get_month_name_from_month(month):
        switcher = {
            1: "Jan",
            2: "Feb",
            3: "Mar",
            4: "Apr",
            5: "May",
            6: "June",
            7: "July",
            8: "Aug",
            9: "Sep",
            10: "Oct",
            11: "Nov",
            12: "Dec"
        }
        return switcher.get(month, "NA")

    @staticmethod
    def convert_hours_to_text(hours):
        if hours < 24:
            hour = int(hours)
            minute = hours - hour
            minutes = int(minute * 60)
            return '{}hr:{}mins'.format(hour, minutes)

        text = '{}D'.format(int(hours / 24))
        remaining_hours = int(hours % 24)
        if remaining_hours:
            text = '{} {}Hr'.format(text, remaining_hours)
        return text

    @staticmethod
    def date_suffix(day):
        if 4 <= day <= 20 or 24 <= day <= 30:
            suffix = 'th'
        else:
            suffix = ['st', 'nd', 'rd'][day % 10 - 1]
        return suffix

    @staticmethod
    def custom_strftime(datetime, include_time=False):
        """Format datetime object to 19th Apr'20."""
        if include_time:
            return datetime.strftime("%-d{} %b'%y %I:%M%p".format(DateUtil.date_suffix(datetime.day)))
        return datetime.strftime("%-d{} %b'%y".format(DateUtil.date_suffix(datetime.day)))

    @staticmethod
    def convert_time_in_hour_to_utc(time_in_hours, date_in_ist):
        hour, minutes = time_in_hours.split(':')
        if int(hour) > 23:
            date_in_ist = DateUtil.convert_to_utc(date_in_ist)
            date_in_ist = date_in_ist + 86400000
            date_in_ist = DateUtil.utc_to_ist_datetime(date_in_ist)
            hour = int(hour) - 24
        date_in_ist = date_in_ist.replace(hour=int(hour), minute=int(minutes), second=0, microsecond=0)
        utc_time = DateUtil.convert_to_utc(date_in_ist)
        return utc_time

    @staticmethod
    def convert_time_in_milliseconds_to_text(milliseconds):
        if not milliseconds:
            return 'N/A'
        seconds = milliseconds / 1000
        hours = seconds / 3600
        if hours < 24:
            hour = int(hours)
            minute = hours - hour
            minutes = int(minute * 60)
            return '{}hr {}mins'.format(hour, minutes)

        text = '{}D'.format(int(hours / 24))
        remaining_hours = int(hours % 24)
        if remaining_hours:
            text = '{} {}Hr'.format(text, remaining_hours)
        return text

    @staticmethod
    def convert_date_str_to_unix(date_str, str_format):
        date_obj = DateUtil.format_to_datetime(date_str, str_format)
        timestamp = DateUtil.convert_to_unix(date_obj)
        return timestamp

    @staticmethod
    def format_timestamp(value, str_format='%Y-%m-%dT%H:%M'):
        if value:
            datetime_obj = DateUtil.utc_to_ist_datetime(value)
            return datetime_obj.strftime(str_format)
        return '-'

    @staticmethod
    def format_timestamp_for_email(value):
        """
        Format timestamp for email templates in the format: "9th April 2025, 09:30 A.M"
        """
        if not value:
            return 'TBD'

        try:
            # Convert epoch timestamp to IST datetime
            datetime_obj = DateUtil.utc_to_ist_datetime(value)

            # Get day with suffix (1st, 2nd, 3rd, 4th, etc.)
            day = datetime_obj.day
            suffix = DateUtil.date_suffix(day)

            # Format the datetime
            # %B = Full month name (April)
            # %Y = Full year (2025)
            # %I = Hour in 12-hour format (09)
            # %M = Minute (30)
            # %p = AM/PM
            formatted_date = datetime_obj.strftime(f"{day}{suffix} %B %Y, %I:%M %p")

            return formatted_date

        except Exception:
            return 'TBD'
