import jwt
import json
import pickle
import redis
import logging
from pydantic import BaseModel, Field, create_model
from pydantic_core import PydanticUndefined
from types import UnionType
from typing import (
    Type, Optional, Any,
    get_origin, get_args
)
from django.conf import settings
from .mongo import MongoUtility
from .common import get_domain_from_url
from .date_util import DateUtil
from .constants import SequenceType, ProcurementDBColls

logger = logging.getLogger('application')

FIELD_ATTRIBUTES = ['gt', 'lt', 'ge', 'le', 'max_length', 'min_length', 'decimal_places']


def generate_rfq_no(company_id, sequence_start=10001):
    db = MongoUtility()

    query = {
        'company_id': company_id,
        'type': SequenceType.RFQ_NO,
        'seq_no': {'$exists': True}
    }
    result = db.update(ProcurementDBColls.SEQUENCE_NUMBER, query, inc_query={'seq_no': 1}, find_one_and_update=True)

    if result is None:
        query = {
            'company_id': company_id,
            'type': SequenceType.RFQ_NO
        }
        set_query = {'seq_no': sequence_start}
        result = db.update(ProcurementDBColls.SEQUENCE_NUMBER, query, set_query=set_query, upsert=True, find_one_and_update=True)

    date = DateUtil.get_current_datetime().strftime('%d-%m-%Y')
    rfq_no = f"{result['seq_no']}-{date}"
    return rfq_no


class JWTToken:
    HS256 = 'HS256'

    class Expiry:
        SSE_EVENTS = 60  # minutes

    class Purpose:
        SSE_EVENTS = 'sse_events'

    @staticmethod
    def get_token(payload: dict, subject: str, purpose: str, expiry: int) -> str:
        issuer = get_domain_from_url(settings.INTERNAL_SERVER_URL)
        issued_at = int(DateUtil.get_current_timestamp() / 1000)
        expires_on = issued_at + (expiry * 60)
        payload.update({
            "iss": issuer,
            "sub": subject,
            "iat": issued_at,
            "exp": expires_on,
            "purpose": purpose,  # custom claim for clarity
        })
        token = jwt.encode(payload, settings.SECRET_KEY, algorithm=JWTToken.HS256)
        return token

    @staticmethod
    def decode_token(token: str, issuer: str = settings.INTERNAL_SERVER_URL, secret: str = settings.SECRET_KEY) -> dict:
        issuer = get_domain_from_url(settings.INTERNAL_SERVER_URL)
        options = {"require": ["iss", "sub", "iat", "exp"]}  # required claims

        try:
            decoded_payload = jwt.decode(token, secret, issuer=issuer, options=options, algorithms=[JWTToken.HS256])
        except (jwt.ExpiredSignatureError, jwt.InvalidIssuerError, jwt.InvalidAudienceError, jwt.MissingRequiredClaimError) as e:
            raise ValueError(f'Token error: {e}')
        except jwt.exceptions.DecodeError:
            raise ValueError(f'Invalid token')
        return decoded_payload


class RedisUtils(object):

    def __init__(self, db_url: str = settings.REDIS_SESSION_DB_URL):
        self.redis_conn = redis.from_url(db_url)

    def list_keys(self):
        """List all keys from current DB."""
        return self.redis_conn.keys('*')

    def get_data(self, key: str) -> dict:
        """Get data stored using JSON format."""
        data = self.redis_conn.get(key)
        return json.loads(data) if data else {}

    def get_pdata(self, key: str) -> dict:
        """Get data stored using Pickle format."""
        data = self.redis_conn.get(key)
        return pickle.loads(data) if data else {}

    def set_data(self, key: str, data: dict) -> bool:
        """Set data using JSON format."""
        return self.redis_conn.set(key, json.dumps(data))

    def set_pdata(self, key: str, data: dict) -> bool:
        """Set data using Pickle format."""
        return self.redis_conn.set(key, pickle.dumps(data))

    def delete_data(self, key: str = None, flush_db: bool = False, flush_all: bool = False) -> int:
        """Delete specific key data or flush_db (current DB) or flush_all (data in all DBs)."""
        res = 0
        if key:
            res = self.redis_conn.delete(key)
        elif flush_db:
            res = self.redis_conn.flushdb()
        elif flush_all:
            res = self.redis_conn.flushall()
        return res


def make_optional(model_cls: Type[BaseModel], strict: bool) -> Type[BaseModel]:
    """Recursively create a new model with optional fields if strict=False."""
    if strict:
        return model_cls

    def extract_field_constraints(metadata):
        field_constraints = {}
        for attr in FIELD_ATTRIBUTES:
            for item in metadata:
                attr_value = getattr(item, attr, None)
                if attr_value is not None:
                    field_constraints[attr] = attr_value
                    break
        return field_constraints

    fields = {}
    for field_name, field_info in model_cls.model_fields.items():
        field_type = field_info.annotation or Any  # Get the type
        default_value = field_info.default  # Preserve default value
        default_factory = field_info.default_factory  # Get default factory if available
        metadata = field_info.metadata

        # Extract existing field metadata
        field_constraints = extract_field_constraints(metadata)

        # Get proper field_type of already Optional fields
        if isinstance(field_type, UnionType):
            field_type = get_args(field_type)[0]

        # Preserve `default` value if set
        if default_value is not PydanticUndefined:
            fields[field_name] = (Optional[field_type], Field(default=default_value, **field_constraints))
        # Preserve `default_factory` if set
        elif default_factory is not None:
            fields[field_name] = (Optional[field_type], Field(default_factory=default_factory, **field_constraints))
        # Otherwise, make it optional
        else:
            fields[field_name] = (Optional[field_type], Field(default=None, **field_constraints))

        # Recursively modify nested models (lists or other BaseModels)
        # Ensure field_type is a class before calling issubclass()
        if isinstance(field_type, type) and issubclass(field_type, BaseModel):
            dynamic_child_model = make_optional(field_type, strict)
            fields[field_name] = (Optional[dynamic_child_model], None)
        # Handle List[BaseModel] cases
        elif get_origin(field_type) is list:
            inner_type = get_args(field_type)[0]  # Extract inner type
            if isinstance(inner_type, type) and issubclass(inner_type, BaseModel):
                dynamic_child_model = make_optional(inner_type, strict)
                fields[field_name] = (Optional[list[dynamic_child_model]], None)

    # Create a new model
    dynamic_model = create_model(
        f"Dynamic{model_cls.__name__}",
        **fields,
        __base__=model_cls,
    )

    return dynamic_model
