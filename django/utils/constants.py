from zoneinfo import ZoneInfo
from enum import Enum


IST_TZ = ZoneInfo('Asia/Kolkata')
UTC_TZ = ZoneInfo('UTC')
XLSX_CONTENT_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'


class ImageTypes(Enum):
    JPG = 'jpg'
    JPEG = 'jpeg'
    PNG = 'png'


class DocumentTypes(Enum):
    PDF = 'pdf'
    XLSX = 'xlsx'
    XLS = 'xls'
    DOCX = 'docx'
    DOC = 'doc'


class VideoTypes(Enum):
    MP4 = 'mp4'
    MOV = 'mov'
    WEBM = 'webm'


# Dynamically create FileTypes Enum
def create_combined_enum(name, *enums):
    combined = {item.name: item.value for enum in enums for item in enum}
    return Enum(name, combined)


FileTypes = create_combined_enum("FileTypes", ImageTypes, DocumentTypes, VideoTypes)


class LongMIMETypes:
    MS_OFFICE = 'application/vnd.ms-office'
    MS_EXCEL = 'application/vnd.ms-excel'
    SHEET = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    MS_WORD = 'application/msword'
    DOCUMENT = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    OCTET_STREAM = 'application/octet-stream'
    BIN_OCTET_STREAM = 'binary/octet-stream'
    PDF = 'application/pdf'
    JPEG = 'image/jpeg'
    PNG = 'image/png'
    MP4 = 'video/mp4'
    MOV = 'video/mov'
    WEBM = 'video/webm'


class ShortMIMETypes:
    JPG = 'jpg'
    JPEG = 'jpeg'
    EXCEL = 'excel'
    XLSX = 'xlsx'
    XLS = 'xls'
    MS_WORD = 'msword'
    DOC = 'doc'
    DOCX = 'docx'
    ZIP = 'zip'
    DATA = 'data'
    ARCHIVE = 'archive'


FILE_TYPE_BACKEND = {
    FileTypes.JPG.value: LongMIMETypes.JPEG,
    FileTypes.JPEG.value: LongMIMETypes.JPEG,
    FileTypes.PNG.value: LongMIMETypes.PNG,
    FileTypes.PDF.value: LongMIMETypes.PDF,
    FileTypes.XLSX.value: LongMIMETypes.SHEET,
    FileTypes.XLS.value: LongMIMETypes.MS_EXCEL,
    FileTypes.DOCX.value: LongMIMETypes.MS_WORD,
    FileTypes.DOC.value: LongMIMETypes.MS_WORD,
    FileTypes.MP4.value: LongMIMETypes.MP4,
    FileTypes.MOV.value: LongMIMETypes.MOV,
    FileTypes.WEBM.value: LongMIMETypes.WEBM,
}

FILE_TYPE_HELPERS = {
    FileTypes.JPG.value: [ShortMIMETypes.JPG, ShortMIMETypes.JPEG, LongMIMETypes.JPEG],
    FileTypes.JPEG.value: [ShortMIMETypes.JPG, ShortMIMETypes.JPEG, LongMIMETypes.JPEG],
    FileTypes.PNG.value: [LongMIMETypes.PNG],
    FileTypes.PDF.value: [LongMIMETypes.PDF],
    FileTypes.XLSX.value: [
        ShortMIMETypes.EXCEL, ShortMIMETypes.XLSX, LongMIMETypes.MS_OFFICE,
        LongMIMETypes.SHEET, LongMIMETypes.MS_EXCEL, LongMIMETypes.OCTET_STREAM,
    ],
    FileTypes.XLS.value: [
        ShortMIMETypes.EXCEL, ShortMIMETypes.XLS, LongMIMETypes.MS_OFFICE,
        LongMIMETypes.MS_EXCEL, LongMIMETypes.OCTET_STREAM,
    ],
    FileTypes.DOCX.value: [
        ShortMIMETypes.DOC, ShortMIMETypes.DOCX, ShortMIMETypes.ZIP, ShortMIMETypes.DATA,
        ShortMIMETypes.ARCHIVE, LongMIMETypes.MS_WORD, LongMIMETypes.MS_OFFICE, LongMIMETypes.DOCUMENT
    ],
    FileTypes.DOC.value: [ShortMIMETypes.MS_WORD, ShortMIMETypes.DOC, LongMIMETypes.MS_WORD, LongMIMETypes.MS_OFFICE],
    FileTypes.MP4.value: [LongMIMETypes.MP4],
    FileTypes.MOV.value: [LongMIMETypes.MOV],
    FileTypes.WEBM.value: [LongMIMETypes.WEBM],
}


class SuccessMessages:
    SUCCESS = 'Success'
    OKAY = 'Looks like everything went okay'
    UPDATE_SUCCESS = 'Data updated successfully'
    RETRIEVE_SUCCESS = 'Data retrieved successfully'


class ErrorMessages:
    INVALID_TOKEN = 'Invalid token'
    ACCESS_DENIED = 'Access Denied'
    TECHNICAL_ERROR = 'OOPS!! Something went wrong. Please try again after sometime.'
    SMS_ALERT = '[SMS_ALERT_ERROR]'
    EMAIL_ALERT = '[EMAIL_ALERT_ERROR]'


class ErrorLogTypes:
    S3_FILE_UPLOAD = '[S3_FILE_UPLOAD_ERROR]'
    S3_FILE_DOWNLOAD = '[S3_FILE_DOWNLOAD_ERROR]'
    S3_FILE_DELETE = '[S3_FILE_DELETE_ERROR]'


class AdminDBColls:
    SUBSCRIPTIONS = 'subscriptions'
    COMPANY_TYPE = 'company_type'
    USER_TYPE = 'user_type'
    USER_ROLES = 'user_roles'
    PERMISSIONS = 'permissions'
    USER_PERMISSIONS = 'user_permissions'
    MODULES = 'modules'
    UI_CONFIG = 'ui_config'
    COMPANIES = 'companies'
    USERS = 'users'
    USER_SESSIONS = 'user_sessions'
    LOGIN_LOGS = 'login_logs'
    USER_ACTIVITY_LOGS = 'user_activity_logs'
    ACCESS_TOKEN = 'access_token'
    OTP_DETAILS = 'otp_details'
    BLOCKED_USERS = 'blocked_users'
    SMS_VENDORS = 'sms_vendors'
    SMS_SENT_LOGS = 'sms_sent_logs'
    API_REQUEST_LOGS = 'api_request_logs'
    STATES_LIST = 'states_list'
    COMPANY_MAPPING = 'company_mapping'
    FUEL_TYPES = 'fuel_types'
    PACKING_TYPES = 'packing_types'
    VEHICLE_TYPES = 'vehicle_types'
    VEHICLE_BODY_TYPES = 'vehicle_body_types'
    BUSINESS_VOLUME_UOMS = 'business_volume_uoms'
    API_KEYS = 'api_keys'
    API_CONFIGS = 'api_configs'
    MODULE_CONFIGS = 'module_configs'
    FEATURE_SETTINGS = 'feature_settings'


class ProcurementDBColls:
    CELERY_SCHEDULES = 'celery_schedules'
    SEQUENCE_NUMBER = 'sequence_number'
    LPP = 'lpp'
    RFQ = 'rfq'
    RFQ_PROVIDERS = 'rfq_providers'
    LANES = 'lanes'
    LANE_PROVIDERS = 'lane_providers'
    BID_TRAIL = 'bid_trail'
    AUCTION_SAVINGS = 'auction_savings'
    EMAIL_LOGS = 'email_logs'


class SAASProduct(Enum):
    NETWORK = 1
    LOGISTICS_PROCUREMENT = 2
    OPTIMIZATION = 3
    EXECUTION = 4
    VISIBILITY = 5
    RECONCILIATION = 6
    ANALYTICS = 7
    ORCHESTRATION = 8
    ILMS = 9


class SAASModules(Enum):
    SUPER_ADMIN = 1
    ADMIN = 2
    PROCUREMENT = 3
    VENDOR_ONBOARDING = 4
    VENDOR_EXPLORER = 5
    FREIGHT_INDEX = 6
    CARBON_EMISSIONS = 7


class SubscriptionStatus(Enum):
    PENDING = 1
    ACTIVE = 2
    PAUSED = 3
    EXPIRED = 4
    CANCELLED = 5
    HALTED = 6
    COMPLETED = 7
    TRIAL = 8
    IN_REVIEW = 9
    REJECTED = 10


class CompanyType(Enum):
    SEEKER = 1
    PROVIDER = 2


class UserType(Enum):
    SEEKER = 1
    PROVIDER = 2
    DRIVER = 3
    CONSIGNEE = 4
    SCLEN = 5


class UserRole(Enum):
    SUPER_ADMIN = 1
    ADMIN_SEEKER = 10
    SEEKER = 20
    PROVIDER = 30


class SMSVendor:
    BOSCHINDIA = 2


class LocationTypes(Enum):
    ZONE = 1
    STATE = 2
    CITY = 3
    ZIPCODE = 4


class RFQStatus(Enum):
    ACTIVE = 1
    BIDDING_IN_PROGRESS = 2
    COMPLETED = 3
    DRAFT = 4


class SequenceType:
    RFQ_NO = 'rfq_no'
    TRIP_NO = 'trip_no'


class AttachmentType:
    TNC = 'tnc'
    CONTRACT = 'contract'
    OTHERS = 'others'


class ContractTenure(Enum):
    SPOT = 'spot'


class StopPointType(Enum):
    DROP = 'Drop'
    PICKUP = 'Pickup'


class TatUOM(Enum):
    DAYS = 'Days'


class BiddingSolution(Enum):
    COMPLETE = 1
    PARTIAL = 2


class BidStatus(Enum):
    NO_BID = 1
    BIDDING = 2
    WITHDRAWN = 3


class ChallengeStatus(Enum):
    NOT_SENT = 1
    SENT = 2
    ACCEPTED = 3
    REJECTED = 4


class CounterStatus(Enum):
    NOT_SENT = 1
    SENT = 2
    ACCEPTED = 3
    REJECTED = 4


class LOIStatus(Enum):
    NOT_SENT = 1
    SENT = 2
    ACCEPTED = 3
    REJECTED = 4
    CANCELLED = 5
    IGNORED = 6


class BiddingActions(Enum):
    BID_PLACED = 'Bid placed by transporter'
    BID_WITHDRAWN = 'Bid withdrawn by transporter'
    CHALLENGE_PRICE_SENT = 'Challenge price sent by seeker'
    CHALLENGE_PRICE_ACCEPTED = 'Challenge price accepted by transporter'
    CHALLENGE_PRICE_REJECTED = 'Challenge price rejected by transporter'
    COUNTER_PRICE_SENT = 'Counter price sent by transporter'
    COUNTER_PRICE_ACCEPTED = 'Counter price accepted by seeker'
    COUNTER_PRICE_REJECTED = 'Counter price rejected by seeker'
    LOI_SENT = 'LOI sent by seeker'
    LOI_ACCEPTED = 'LOI accepted by transporter'
    LOI_REJECTED = 'LOI rejected by transporter'
    LOI_CANCELLED = 'LOI cancelled by seeker'


class BiddingCategory(Enum):
    MONITORED = 1
    UNMONITORED = 2


class VendorBiddingScreen(Enum):
    BLIND_BIDDING = 1
    RANK_INDICATOR = 2
    L1_RATE = 3
    LIVE_STATUS = 4
    L1_AND_LIVE_STATUS = 5


class ApiType(Enum):
    GOOGLE_GEOCODE_API = 1
    GOOGLE_PLACES_API = 2
    GOOGLE_DIRECTIONS_API = 3


class BidCompetency(Enum):
    HIGH = 'High'
    MEDIUM = 'Medium'
    LOW = 'Low'


class RedisChannel(Enum):
    BIDDING_STATUS = 'bidding_status'
    RFQ_STATUS_UPDATE = 'rfq_status_update'
    AUCTION_UPDATES = 'auction_updates'


class ServerSideEvent:
    UPDATE = 'UPDATE'
    TOKEN_EXPIRED = 'TOKEN_EXPIRED'
    CONNECTION_TIMEOUT = 'CONNECTION_TIMEOUT'
