# Install Docker

Mac: https://docs.docker.com/docker-for-mac/install/

Windows: https://docs.docker.com/docker-for-windows/install/

### Configure Docker
Navigate to docker `Preferences` > `Advanced`:

* Set CPUs to 2
* Memory to 2GB
* Click Apply & Restart

# Project Setup

1.Fork the repo into your bitbucket account.

2.Clone the forked repo and navigate into the project folder

   `git clone https://<Your Bitbucket Username>@bitbucket.org/<Your Bitbucket Username>/<Repo Name>.git`

2.Build docker (Check if docker is up and running):

   `docker-compose up --build -d`

3.For manually running django server:

   `docker-compose exec -u root django bash`

   `python manage.py migrate`

   `python manage.py runserver 0:5000`

### docker-compose too slow (or hangs) ?
If your docker-compose commands takes a lot of time to start or hangs, it might be because, it tries to ping certain sockets. This problem can be prevented by adding the below lines to the end of the `/etc/hosts` file.

Open registry file using command `vi /etc/hosts`

Press `i` to insert text

Append
```
127.0.0.1 localunixsocket
127.0.0.1 localunixsocket.lan
127.0.0.1 localunixsocket.local
```
to the end of the file (only if these lines are not present anywhere in the file)

Save and Quit: Press `Esc` Type `:wq` Press `Enter`

### Quit Docker

While in the project directory, Execute `docker-compose down --remove-orphans`
